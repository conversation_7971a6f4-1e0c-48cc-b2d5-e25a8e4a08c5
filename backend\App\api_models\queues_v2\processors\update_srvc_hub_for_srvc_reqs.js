const { getServiceHubsModelFrQueue } = require('./helpers/service_hubs_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const service_hubs_model = getServiceHubsModelFrQueue(
        app,
        job.data.service_hubs_model_data
    );

    let { query, operation } = job.data;

    if (operation === 'assign_new_hub') {
        await service_hubs_model.assignNewHubToServiceRequests(query);
    } else if (operation === 'update_existing_hub') {
        await service_hubs_model.updateExistingHubForServiceRequests(query);
    } else {
        console.error(`Unknown operation: ${operation}`);
    }

    done(null, {});
};

exports.default = performJob;
