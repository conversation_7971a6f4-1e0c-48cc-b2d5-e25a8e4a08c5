CREATE OR REPLACE FUNCTION public.tms_get_sbtsk_dump_fr_vertical(start_date_ timestamp, end_date_ timestamp, materialized_table_name_ text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	status boolean;
	message text;
	resp_data json;
	_dynamic_sql text;
	
begin
	status = false;
	message = 'Internal_error';
	
	-- Create materialized view with sbtsk data for sbtsk_type_id = 243
	_dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS
						select
							sbtsk.db_id as "Subtask ID",
							sbtsk.srvc_req_id as "Service Request ID",
							sbtsk.sbtsk_type as "Subtask Type ID",
							sbtsk.status as "Status",
							sbtsk.priority as "Priority",
							DATE(sbtsk.start_time) as "Start Date",
							DATE(sbtsk.end_time) as "End Date",
							TO_CHAR(sbtsk.start_time::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) as "Start Time",
							TO_CHAR(sbtsk.end_time::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) as "End Time",
							DATE((sbtsk.c_meta).time) as "Created Date",
							TO_CHAR(((sbtsk.c_meta).time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$, $$HH12:MIPM$$) as "Created Time",
							c_by_usr."name" as "Created By",
							srvc_req.display_code as "Service Display Code",
							org.nickname as "Organization Name",
							sbtsk_type.title as "Subtask Type Title",
							sbtsk_status.title as "Status Title"
						  from public.cl_tx_sbtsk as sbtsk
						 inner join cl_tx_srvc_req as srvc_req
						    on srvc_req.db_id = sbtsk.srvc_req_id
						 inner join cl_tx_orgs as org
						    on org.org_id = sbtsk.org_id
						 inner join cl_cf_sbtsk_types as sbtsk_type
						    on sbtsk_type.sbtsk_type_id = sbtsk.sbtsk_type
						 inner join cl_cf_sbtsk_statuses as sbtsk_status
						    on sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type
						   and sbtsk_status.status_key = sbtsk.status
						 inner join cl_tx_users as c_by_usr
						    on c_by_usr.usr_id = sbtsk.c_by
						 where sbtsk.sbtsk_type = 243
						   and sbtsk.is_deleted is not true
						   and DATE(sbtsk.start_time) >= DATE($$' || start_date_ || '$$)
						   and DATE(sbtsk.start_time) <= DATE($$' || end_date_ || '$$)
						 order by sbtsk.db_id desc';
	
	-- Execute the dynamic SQL to create materialized view
	execute _dynamic_sql;
	
	-- Return success response
	status = true;
	message = 'Materialized view created successfully';
	resp_data = jsonb_build_object(
		'materialized_view_name', materialized_table_name_,
		'start_date', start_date_,
		'end_date', end_date_,
		'filter_sbtsk_type_id', 243
	);
	
	return jsonb_build_object('status', status, 'code', message, 'data', resp_data);
	
exception
	when others then
		status = false;
		message = 'Error creating materialized view: ' || SQLERRM;
		return jsonb_build_object('status', status, 'code', message, 'data', null);
		
end;
$function$
;
