const setParamsToModel = (model, dataObj, mainDb, replicaDb) => {
    model.db = mainDb;
    model.databaseReplica = replicaDb;
    model.ip_addr = dataObj.ip_addr;
    model.user_agent = dataObj.user_agent;
    model.user_context = dataObj.user_context;
};

exports.getSrvcTypeModelFrQueue = (app, model_data) => {
    const model = require('../../../setup/srvc_types_model').getInstance();
    setParamsToModel(model, model_data, app.get('db'), app.get('db_replica'));
    return model.getFreshInstance(model);
};
