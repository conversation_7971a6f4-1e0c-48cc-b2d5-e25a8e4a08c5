const {
    getRandomInteger,
    removeAndAppendSixDigitIntegers,
    formatDate,
} = require('../../utils/helper');
const { allQueues } = require('../queues');
const { getUsersModelFrQueue } = require('./helpers/users_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const jobData = job.data;
    let truncatedAddress = jobData.srvc_req_address;
    let {
        srvc_req_address,
        sbtsk_end_time,
        sbtsk_start_time,
        sbtsk_type_name,
        sbtsk_ticket_id,
        sbtsk_assignee,
        sbtsk_start_day,
        sbtsk_entry_id,
        srvc_req_id,
    } = jobData;
    if (truncatedAddress) {
        truncatedAddress = removeAndAppendSixDigitIntegers(truncatedAddress);
        const firstPart = truncatedAddress.slice(0, 45);
        const lastPart = truncatedAddress.slice(-6);
        truncatedAddress = `${firstPart}...${lastPart}`;
        truncatedAddress = `${truncatedAddress} \n`;
    }

    if (sbtsk_start_day) {
        sbtsk_start_day = formatDate(sbtsk_start_day);
    }

    console.log(
        'send_new_sbtsk_creation_notification :: performJob :: data :: ',
        jobData
    );

    let data = {
        end_time: sbtsk_end_time,
        start_time: sbtsk_start_time,
        address: srvc_req_address,
        sbtsk_type_name: sbtsk_type_name,
        sbtsk_ticket_id: sbtsk_ticket_id,
        sbtsk_assignee: sbtsk_assignee?.key
            ? sbtsk_assignee?.key
            : sbtsk_assignee?.value,
        sbtsk_start_day: sbtsk_start_day,
        truncatedAddress,
    };

    let notification_title = `New ${sbtsk_type_name} assigned`;
    let notification_message = `Date : ${sbtsk_start_day}  ${sbtsk_start_time}\n${truncatedAddress}REQUEST ID : ${sbtsk_ticket_id}`;
    let urlSearchParam = new URLSearchParams();
    urlSearchParam.append(
        'filters',
        JSON.stringify({
            sbtsk_due_day: ['all'],
            sbtsk_assgn_by_me: ['sbtsk_id'],
        })
    );
    urlSearchParam.append('query', sbtsk_entry_id);
    urlSearchParam.append('srvcReqId', srvc_req_id);
    let redirect_url = `/my-tasks?${urlSearchParam.toString()}`;
    let notification_id = JSON.stringify(getRandomInteger(1, 999));
    try {
        const users_model = getUsersModelFrQueue(
            app,
            jobData.subtasks_model_data
        );
        const userLogins = await users_model.getUserLogins(data.sbtsk_assignee);
        const userLoginsResp = JSON.parse(userLogins.resp);

        if (userLoginsResp && userLoginsResp?.length > 0) {
            for (let single_logged_in_user_detail of userLoginsResp[0]
                .logged_in_user_details) {
                if (single_logged_in_user_detail.fcm_id) {
                    allQueues.WIFY_NOTIFICATION_SEND_FCM.addJob({
                        fcm_token: single_logged_in_user_detail.fcm_id,
                        title: notification_title,
                        message: notification_message,
                        form_data: data,
                        notification_type: 'task_assignee',
                        notificationContentType: 'taskReminder',
                        redirect_url,
                        noti_id: notification_id,
                    });
                }
            }
        }
    } catch (error) {
        console.log('send_new_sbtsk_creation_notification :: error', error);
    }
    try {
        allQueues.WIFY_TMS_ADD_NOTIFICATION_LOG.addJob({
            user_info: {
                org_id: jobData.org_id,
                usr_id: data.sbtsk_assignee,
                ip_address: jobData.ip_address,
                user_agent: jobData.user_agent,
            },
            user_id: data.sbtsk_assignee,
            module: 'subtask',
            module_id: 1,
            channel: 'fcm',
            notification_title: notification_title,
            notification_message: notification_message,
            form_data: jobData,
            notification_type: 'task_assignee',
            redirect_url,
            truncatedAddress,
            notification_id,
        });
    } catch (error) {
        //swallow
        console.log(
            'send_new_sbtsk_creation_notification :: add Log :: error',
            error
        );
    }

    done(null, {});
};
exports.default = performJob;
