const internalProcessJobTypes = {
    geoStatusRefresh: 'geoStatusRefresh',
    deleteApiLogs: 'deleteApiLogs',
};

const {
    getInternalOperationsFrQueue,
} = require('./helpers/internal_operations_helper');

async function updateGeoVerificationStatus(data) {
    try {
        // console.log(
        //     'process internal operations :: updateGeoVerificationStatus :: data in queue processor::',
        //     data
        // );
        const app = require('./../../../app');
        const internalOperationsModel = getInternalOperationsFrQueue(app, {});
        const updateSubtask =
            await internalOperationsModel.updateGeoStatus(data);
    } catch (error) {
        console.log(
            'process internal operations :: updateGeoVerificationStatus :: error :: ',
            error
        );
    }
}

async function processApiLogsDeletion(data) {
    try {
        const app = require('./../../../app');
        const internalOperationsModel = getInternalOperationsFrQueue(app, {});
        const deletionResult =
            await internalOperationsModel.processApiLogsDeletion(data);
        console.log(
            'process internal operations :: processApiLogsDeletion :: result ::',
            deletionResult
        );
    } catch (error) {
        console.log(
            'process internal operations :: processApiLogsDeletion :: error :: ',
            error
        );
    }
}

const performJob = async (job, done) => {
    let { jobType } = job.data;
    switch (jobType) {
        case internalProcessJobTypes.geoStatusRefresh:
            updateGeoVerificationStatus(job.data);
            break;

        case internalProcessJobTypes.deleteApiLogs:
            processApiLogsDeletion(job.data);
            break;

        default:
            console.log(
                'process internal operations :: performJob :: Invalid job type'
            );
            break;
    }
    done(null, {});
};

exports.default = performJob;
exports.internalProcessJobTypes = internalProcessJobTypes;
