const AWS = require('aws-sdk');
const lambda = new AWS.Lambda({
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
    region: process.env.REGION,
    signatureVersion: process.env.SIGNATUREVERSION,
});
/**
 * validate and update subtask status by aws lambda function.
 * @param {{
 * FunctionName   : string,
 * InvocationType : string,
 * LogType : string
 * Payload : string
 * }} params
 * @return {Promise<AWS.Lambda.InvocationResponse>}
 */
const callLambdaFn = (params) => {
    return new Promise((resolve, reject) => {
        try {
            lambda.invoke(params, (error, data) => {
                if (error) {
                    console.log('callLambdaFn :: error :: ', error);
                    reject(error);
                } else {
                    if (data.StatusCode == 200) {
                        resolve(data);
                    } else {
                        reject(new Error(data));
                    }
                }
            });
        } catch (error) {
            console.log('callLambdaFn :: catch block :: error :: ', error);
            reject(error);
        }
    });
};

/**
 * Call a Node.js server running behind Lambda with endpoints using AWS Lambda SDK
 * This function creates an API Gateway proxy event to invoke the Lambda function
 * @param {{
 * FunctionName  : string,  // Lambda function name or ARN
 * endpoint      : string,  // API endpoint path (e.g., /api/resource)
 * method        : string,  // HTTP method (GET, POST, PUT, DELETE)
 * data          : object,  // Request payload
 * queryParams   : object,  // Query parameters
 * headers       : object,  // Request headers
 * path          : string,  // Path parameters
 * }} options
 * @return {Promise<AWS.Lambda.InvocationResponse>} Lambda response
 */
const callLambdaWithApiGatewayEvent = (options) => {
    return new Promise((resolve, reject) => {
        try {
            const {
                FunctionName,
                endpoint = '/',
                method = 'GET',
                data = {},
                queryParams = {},
                headers = {},
                path = {},
            } = options;

            if (!FunctionName) {
                return reject(new Error('FunctionName is required'));
            }

            // Create an API Gateway proxy event
            // This mimics what API Gateway would send to the Lambda function
            const apiGatewayEvent = {
                httpMethod: method.toUpperCase(),
                path: endpoint,
                queryStringParameters: queryParams,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers,
                },
                pathParameters: path,
                body: JSON.stringify(data),
                isBase64Encoded: false,
            };

            const params = {
                FunctionName,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(apiGatewayEvent),
            };

            lambda.invoke(params, (error, data) => {
                if (error) {
                    console.log(
                        'callLambdaWithApiGatewayEvent :: error :: ',
                        error
                    );
                    reject(error);
                } else {
                    if (data.StatusCode == 200) {
                        if (typeof data.Payload === 'string') {
                            // Check if it looks like HTML
                            if (data.Payload.startsWith('<')) {
                                console.log(
                                    'callLambdaWithApiGatewayEvent :: Lambda returned HTML instead of JSON'
                                );
                                resolve({
                                    StatusCode: 500,
                                    Payload: JSON.stringify({
                                        success: false,
                                        message:
                                            'Lambda returned HTML instead of JSON',
                                        errorType: 'HTML_RESPONSE',
                                    }),
                                    isHtmlResponse: true,
                                });
                                return;
                            }
                        }

                        // Parse the Lambda response
                        try {
                            const lambdaResponse = JSON.parse(data.Payload);

                            // If the Lambda function returns an API Gateway proxy response
                            if (
                                lambdaResponse.statusCode &&
                                lambdaResponse.body
                            ) {
                                // Try to parse the body if it's a string
                                let parsedBody;
                                try {
                                    parsedBody =
                                        typeof lambdaResponse.body === 'string'
                                            ? JSON.parse(lambdaResponse.body)
                                            : lambdaResponse.body;
                                } catch (bodyParseError) {
                                    console.log(
                                        'callLambdaWithApiGatewayEvent :: Error parsing response body'
                                    );
                                    parsedBody = lambdaResponse.body;
                                }

                                resolve({
                                    StatusCode: lambdaResponse.statusCode,
                                    Payload: JSON.stringify(parsedBody),
                                    Headers: lambdaResponse.headers || {},
                                });
                            } else {
                                // If it's a direct Lambda response
                                resolve({
                                    StatusCode: data.StatusCode,
                                    Payload: data.Payload,
                                    Headers: {},
                                });
                            }
                        } catch (parseError) {
                            console.log(
                                'callLambdaWithApiGatewayEvent :: parse error :: ',
                                parseError
                            );
                            // Return a formatted error response
                            resolve({
                                StatusCode: 500,
                                Payload: JSON.stringify({
                                    success: false,
                                    message:
                                        'Error parsing Lambda response: ' +
                                        parseError.message,
                                    errorType: 'PARSE_ERROR',
                                }),
                                parseError: true,
                            });
                        }
                    } else {
                        reject(new Error(JSON.stringify(data)));
                    }
                }
            });
        } catch (error) {
            console.log(
                'callLambdaWithApiGatewayEvent :: catch block :: error :: ',
                error
            );
            reject(error);
        }
    });
};

module.exports = {
    callLambdaFn,
    callLambdaWithApiGatewayEvent,
};
