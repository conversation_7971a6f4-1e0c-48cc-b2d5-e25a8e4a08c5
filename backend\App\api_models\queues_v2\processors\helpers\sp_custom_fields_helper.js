const getSpCustomFieldsModelFrQueue = (app, sp_custom_fields_model_data) => {
    const sp_custom_fields = require('../../../sp_custom_fields').getInstance();
    sp_custom_fields.database = app.get('db');
    sp_custom_fields.ip_addr = sp_custom_fields_model_data.ip_address;
    sp_custom_fields.user_agent = sp_custom_fields_model_data.user_agent;
    sp_custom_fields.user_context = sp_custom_fields_model_data.userContext;
    return sp_custom_fields;
};

module.exports = {
    getSpCustomFieldsModelFrQueue,
};
