const { setParamsToUserModel } = require('../queues/processors/helpers');
const { allQueues } = require('../queues_v2/queues');
const users_model = require('../users_model');
const cacher = require('../utils/cacher');

class service_type_workflow {
    constructor(model) {
        this.model = model.getFreshInstance(model);
    }

    trigger(query, entry_id) {
        if (entry_id > 0) {
            // log query
            // console.log('trigger query::', query);
            this.triggerUpdateWorkflow(query, entry_id);
        }
    }

    triggerUpdateWorkflow(query, entry_id) {
        let jobData = { query, entry_id };
        jobData['service_type_model_data'] = this.model.getModelData(
            this.model
        );
        allQueues.WIFY_SRVC_TYPE_UPDATE_WRKFLW.addJob(jobData);
    }

    async processUpdateWorkFlow(query, entry_id, mainDb) {
        // console.warn('processUpdateWorkFlow:: Pending implementation ');
        // if query has srvc_type_periodic_automation key
        if (query.srvc_type_periodic_automation) {
            await this.processPeriodicAutomations(query, entry_id, mainDb);
        }

        if (
            query.srvc_prvdr_restricted_request_status &&
            query.isFeatureEnabledFrTMS250414073641
        ) {
            await this.processSrvcPrvdrRestrictedRequestStatus({
                query,
                entry_id,
                mainDb,
            });
        }
    }

    async processPeriodicAutomations(query, entry_id, mainDb) {
        if (query.srvc_type_periodic_automation == undefined) {
            return;
        }

        let periodic_automations = [];
        try {
            periodic_automations = JSON.parse(
                query.srvc_type_periodic_automation
            );
        } catch (error) {
            console.error('Error parsing periodic_automations:', error);
            return;
        }

        // delete all the jobs whose id is starting with ${org_id}_${entry_id}
        let org_id = users_model.getOrgId(this.model.userContext);
        let jobIdPrefix = `${org_id}_${entry_id}_`;
        allQueues.CRON_SRVC_TYPE_PERIODIC_AUTOMATIONS.deleteJobByPrefix(
            jobIdPrefix
        );
        for (let automation of periodic_automations) {
            let { key, cron_frequency } = automation;
            let jobId = `${jobIdPrefix}${key}`;
            const jobData = {
                srvc_type_id: entry_id,
                org_id,
                ...automation,
            };
            allQueues.CRON_SRVC_TYPE_PERIODIC_AUTOMATIONS.addJob(jobData, {
                repeat: { cron: cron_frequency },
                jobId: jobId, // Custom job ID
            });
        }
    }

    async processSrvcPrvdrRestrictedRequestStatus({ query, entry_id, mainDb }) {
        try {
            if (query.srvc_prvdr_restricted_request_status == undefined) {
                return;
            }
            console.log(
                'service_type_workflow :: processSrvcPrvdrRestrictedRequestStatus :: Start'
            );
            let jobData = { query, entry_id };
            jobData['service_type_model_data'] = this.model.getModelData(
                this.model
            );

            allQueues.PROCESS_SRVC_PRVDR_RESTRICT_REQ_STATUS.addJob(jobData);
        } catch (error) {
            console.log(
                'service_type_workflow :: processSrvcPrvdrRestrictedRequestStatus :: error',
                error
            );
            return;
        }
    }

    getFreshInstance(model) {
        const clonedInstance = new service_type_workflow(model.model);
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = service_type_workflow;
