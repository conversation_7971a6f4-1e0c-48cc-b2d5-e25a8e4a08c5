const setParamsToModel = (model, dataObj, mainDb, replicaDb) => {
    model.database = mainDb;
    model.databaseReplica = replicaDb;
    model.ip_addr = dataObj.ip_addr;
    model.user_agent = dataObj.user_agent;
    model.user_context = dataObj.user_context;
};

exports.getDataTransformModelFrQueue = (app, model_data) => {
    const model = require('../../../data_transform_model').default;
    setParamsToModel(model, model_data, app.get('db'), app.get('db_replica'));
    return model;
};
