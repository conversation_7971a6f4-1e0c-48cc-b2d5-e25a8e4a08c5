name: DepCheck

on:
    workflow_dispatch: # Allows manual execution from GitHub Actions UI
    push:
        branches:
            - main

jobs:
    # Check if workflow should run based on changed files
    check-changes:
        runs-on: ubuntu-latest
        outputs:
            should-run: ${{ steps.changes.outputs.should_run }}
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Check for relevant changes
              id: changes
              run: |
                  # Get the list of changed files
                  if [ "${{ github.event_name }}" = "push" ]; then
                      # For push events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      # For manual dispatch, always run
                      echo "Manual workflow dispatch - running dependency checks"
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      exit 0
                  else
                      # For other events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  fi

                  echo "Changed files:"
                  echo "$CHANGED_FILES"
                  echo ""

                  # Check if there are changes in backend or frontend folders
                  if echo "$CHANGED_FILES" | grep -q "^backend/\|^frontend/"; then
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      echo "✅ Changes detected in backend or frontend folders - running dependency checks"

                      # Show which areas changed
                      if echo "$CHANGED_FILES" | grep -q "^backend/"; then
                          echo "📁 Backend changes detected"
                      fi
                      if echo "$CHANGED_FILES" | grep -q "^frontend/"; then
                          echo "📁 Frontend changes detected"
                      fi
                  else
                      echo "should_run=false" >> $GITHUB_OUTPUT
                      echo "⏭️ No changes in backend or frontend folders - skipping dependency checks"
                      echo "Changed files are in other directories (e.g., .github, docs, etc.)"
                  fi

    security_scan:
        name: Run Dependency Checks
        runs-on: ubuntu-latest
        needs: check-changes
        if: needs.check-changes.outputs.should-run == 'true'

        strategy:
            matrix:
                project:
                    - { name: 'Auth', path: './backend/Auth' }
                    - { name: 'App', path: './backend/App' }

        steps:
            - name: Check if this project should run
              id: should-run
              run: |
                  PROJECT_CONDITION="${{ matrix.project.condition }}"
                  if [ "$PROJECT_CONDITION" = "auth-changed" ] && [ "${{ needs.detect-changes.outputs.auth-changed }}" = "true" ]; then
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      echo "✅ Running dependency check for ${{ matrix.project.name }} (changes detected)"
                  elif [ "$PROJECT_CONDITION" = "app-changed" ] && [ "${{ needs.detect-changes.outputs.app-changed }}" = "true" ]; then
                      echo "should_run=true" >> $GITHUB_OUTPUT
                      echo "✅ Running dependency check for ${{ matrix.project.name }} (changes detected)"
                  else
                      echo "should_run=false" >> $GITHUB_OUTPUT
                      echo "⏭️ Skipping dependency check for ${{ matrix.project.name }} (no changes detected)"
                  fi

            - name: Checkout Repository
              if: steps.should-run.outputs.should_run == 'true'
              uses: actions/checkout@v4

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - name: Ensure Lock File Exists
              working-directory: ${{ matrix.project.path }}
              run: |
                  if [ ! -f package-lock.json ]; then
                    echo "Generating package-lock.json..."
                    npm install --package-lock-only
                  fi

            - name: Install Dependencies
              working-directory: ${{ matrix.project.path }}
              run: npm ci

            - name: Run OWASP Dependency-Check
              uses: dependency-check/Dependency-Check_Action@main
              with:
                  project: '${{ matrix.project.name }}'
                  format: 'HTML'
                  out: '${{ matrix.project.path }}/dependency-check-report'

            - name: Upload Dependency-Check Report
              uses: actions/upload-artifact@v4
              with:
                  name: dependency-check-report-${{ matrix.project.name }}
                  path: ${{ matrix.project.path }}/dependency-check-report

    send_slack_report:
        name: Send Slack Message
        runs-on: ubuntu-latest
        needs: [check-changes, security_scan]
        if: needs.check-changes.outputs.should-run == 'true'

        steps:
            - name: Generate Slack Message
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
                  SLACK_CHANNEL_ID: 'C08HTNPESP8' # Replace with your actual Slack Channel ID
                  GITHUB_RUN_URL: 'https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}'
              run: |
                  MESSAGE="📢 *Dependency Check Reports Available* \n\n\
                  ##############################\n\
                  🔍 *Security Scan Results:* \n\
                  ##############################\n\n\
                  The latest dependency check reports are available in GitHub Actions. \n\n"

                  REPORTS_LIST=""

                  for PROJECT in "Auth" "App"; do
                      REPORTS_LIST+="🔹 *$PROJECT Report:* [View Report]($GITHUB_RUN_URL)\n"
                  done

                  MESSAGE+="$REPORTS_LIST 🚀"

                  curl -X POST https://slack.com/api/chat.postMessage \
                    -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
                    -H "Content-Type: application/json" \
                    --data '{
                      "channel": "'"$SLACK_CHANNEL_ID"'",
                      "text": "'"$MESSAGE"'"
                    }'
