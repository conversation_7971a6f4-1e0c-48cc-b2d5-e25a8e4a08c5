const app = require('../../../app');

/**
 * Helper function to get the capacity update model with proper initialization
 * @returns {Object} - Initialized capacity update model
 */
const getCapacityUpdateModel = () => {
    const model = require('../../capacity_update/capacity_update_model');
    model.db = app.get('db');
    model.databaseReplica = app.get('db_replica');
    return model.getFreshInstance(model);
};

/**
 * Process the capacity update job
 * @param {Object} job - The job object from the queue
 * @param {Function} done - Callback to be called when the job is done
 */
const performJob = async (job, done) => {
    console.log(`org_${job.data.org_id} | JOB: capacity_update`);

    try {
        // Get the capacity update model
        const capacityUpdateModel = getCapacityUpdateModel();

        // Set IP and user agent from job data if available
        if (job.data.ip_address) {
            capacityUpdateModel.ip_addr = job.data.ip_address;
        }

        if (job.data.user_agent) {
            capacityUpdateModel.user_agent = job.data.user_agent;
        }

        // Log the job data (more concise)
        console.log(
            `org_${job.data.org_id} | DATA: ${JSON.stringify({
                query: job.data.query || {},
                org_name: job.data.org_name,
                timestamp: job.data.timestamp,
            })}`
        );

        // Call the model's processCapacityUpdate method
        const result = await capacityUpdateModel.processCapacityUpdate(
            job.data
        );

        // Mark the job as completed with the result from the model
        done(null, result);
    } catch (error) {
        console.error('Error processing capacity update job:', error);
        // Mark the job as failed, but it will be retried based on the queue configuration
        done(error);
    }
};

exports.default = performJob;
