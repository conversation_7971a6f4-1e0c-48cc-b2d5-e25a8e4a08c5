# Lambda API Helpers

This document describes how to use the Lambda API helpers to call Node.js servers running behind Lambda functions with API endpoints.

## Overview

The Lambda API helpers provide a way to call Node.js servers running behind Lambda functions with API endpoints. This is useful when you have a Lambda function that hosts an Express.js server or similar, and you want to call specific API endpoints on that server.

## Available Functions

### `callLambdaWithApiGatewayEvent`

This function is available in `api_models/utils/lambda_helpers.js` and allows you to call a Lambda function with an API Gateway proxy event. This mimics what API Gateway would send to the Lambda function.

```javascript
const {
    callLambdaWithApiGatewayEvent,
} = require('./api_models/utils/lambda_helpers');

const options = {
    FunctionName: 'my-lambda-function',
    endpoint: '/api/resource',
    method: 'GET',
    data: {}, // Request body
    queryParams: { param1: 'value1' }, // Query parameters
    headers: { 'x-custom-header': 'value' }, // Custom headers
    path: {}, // Path parameters
};

const result = await callLambdaWithApiGatewayEvent(options);
```

### `processLambdaApiCall`

This is a higher-level wrapper function available in `api_models/queues_v2/processors/helpers/lambda_helper.js` that makes it easier to call Lambda functions with API endpoints. It handles response formatting and error handling.

```javascript
const {
    processLambdaApiCall,
} = require('./api_models/queues_v2/processors/helpers/lambda_helper');

const result = await processLambdaApiCall(
    'my-lambda-function', // Lambda ARN or name
    '/api/resource', // Endpoint path
    'GET', // HTTP method
    {}, // Request body
    { param1: 'value1' }, // Query parameters
    { 'x-custom-header': 'value' } // Custom headers
);
```

## Environment Variables

The `processLambdaApiCall` function uses the following environment variables:

- `BOOKINGS_SERVICE_API_KEY`: The API key to use for authentication with the bookings service.

Make sure to set these environment variables in your application's environment.

## Error Handling

The `processLambdaApiCall` function returns a `sampleOperationResp` object with the following properties:

- `success`: A boolean indicating whether the call was successful.
- `resp`: The response data from the Lambda function.
- `httpStatus`: The HTTP status code of the response.

If an error occurs, the `success` property will be `false`, the `resp` property will contain an error message, and the `httpStatus` property will be an appropriate error status code.
