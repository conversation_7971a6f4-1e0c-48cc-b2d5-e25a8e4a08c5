const {
    getSpCustomFieldsModelFrQueue,
} = require('./helpers/sp_custom_fields_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const sp_custom_fields = getSpCustomFieldsModelFrQueue(
        app,
        job.data.sp_custom_fields_model_data
    );

    let { query, operation } = job.data;

    if (operation === 'update') {
        await sp_custom_fields.updateVerticalForServiceRequests(query);
    } else if (operation === 'remove') {
        await sp_custom_fields.removeVerticalFromServiceRequests(query);
    } else {
        console.error(`Unknown operation: ${operation}`);
    }

    done(null, {});
};

exports.default = performJob;
