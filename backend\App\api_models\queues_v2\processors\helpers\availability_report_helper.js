const setParamsToAvailabilityReportModel = (
    availabilityReportModel,
    dataObj,
    mainDb,
    replicaDb,
    DumpDb
) => {
    availabilityReportModel.db = mainDb;
    availabilityReportModel.databaseReplica = replicaDb;
    availabilityReportModel.ip_addr = dataObj.ip_addr;
    availabilityReportModel.user_agent = dataObj.user_agent;
    availabilityReportModel.user_context = dataObj.user_context;
    availabilityReportModel.databaseDump = DumpDb;
};

exports.getAvailabilityReportModelFrQueue = (app, model_data) => {
    const availabilityReportModel = require('../../../ace/availability_report_model');
    setParamsToAvailabilityReportModel(
        availabilityReportModel,
        model_data,
        app.get('db'),
        app.get('db_replica'),
        app.get('db_dump')
    );
    return availabilityReportModel.getFreshInstance(availabilityReportModel);
};
