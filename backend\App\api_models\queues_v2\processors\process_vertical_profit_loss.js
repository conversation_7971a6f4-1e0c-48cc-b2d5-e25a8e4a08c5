const { getServiceModelFrQueue } = require('./helpers/services_helper');

const performJob = async (job, done) => {
    console.log('pnlcron debug initiated the queue');
    const app = require('../../../app');
    const { query, srvcReqs, services_model_data } = job.data;
    const services_model = getServiceModelFrQueue(app, services_model_data);

    console.log(
        'pnlcron debug srvc reqs inside processor :: ',
        JSON.stringify(srvcReqs)
    );

    const profitLossData = [];
    // Process each service request
    for (const srvcReq of srvcReqs) {
        const profitLossQuery = {
            ...query,
            srvc_req_id: srvcReq.srvc_req_id,
            srvc_type_id: srvcReq.srvc_type_id,
            org_nick_name: srvcReq.org_nick_name,
        };

        // Set model properties for the API call
        services_model.srvcTypeId = srvcReq.srvc_type_id;
        services_model.srvcReqId = srvcReq.srvc_req_id;

        try {
            console.log(
                'pnlcron debug calling services model from cron :: ',
                profitLossQuery.srvc_req_id
            );
            const profitLossResp = await services_model.getProfitAndLossData(
                profitLossQuery,
                profitLossQuery.srvc_req_id
            );

            if (profitLossResp.isSuccess()) {
                const data = JSON.parse(profitLossResp.resp);
                profitLossData.push({
                    srvc_req_id: srvcReq.srvc_req_id,
                    srvc_type_id: srvcReq.srvc_type_id,
                    display_code: srvcReq.display_code,
                    profit_and_loss_data: data,
                });
            }
        } catch (error) {
            console.error(
                `Error processing profit and loss for service request ${srvcReq.srvc_req_id}:`,
                error
            );
        }
    }

    // Store the results or send notifications as needed
    // For example, you could store the results in a database or send an email

    console.log(
        `Processed profit and loss data for ${profitLossData.length} service requests`
    );

    done(null, { profitLossData });
};

exports.default = performJob;
