const {
    getDataTransformModelFrQueue,
} = require('./helpers/data_transform_helper');

/**
 * Queue processor for updating prvdr_vertical column in service requests
 * Uses the existing updateVerticalForServiceRequests method from sp_custom_fields model
 */
const performJob = async (job, done) => {
    try {
        const app = require('../../../app');

        const {
            query,
            data_transform_model_data,
            batchSize,
            batchNumber,
            totalBatches,
        } = job.data;

        console.log(
            `ServiceType::${query.srvc_type_id}::Processing batch ${batchNumber}/${totalBatches} `
        );

        // Get the data transform model from queue
        const dataTransformModel = getDataTransformModelFrQueue(
            app,
            data_transform_model_data
        );

        // Call the model method to update the batch
        const updateResult =
            await dataTransformModel.processUpdateSrvcReqPrvdrVerticalBatch(
                query
            );
        if (!updateResult.isSuccess()) {
            console.error(
                `ServiceType::${query.srvc_type_id}::Batch ${batchNumber} failed:`,
                updateResult.resp
            );
            throw new Error(updateResult.resp || 'Failed to update batch');
        }

        const resultData = JSON.parse(updateResult.resp);
        const updatedCount = resultData.updated_count || 0;

        console.log(
            `ServiceType::${query.srvc_type_id}::Updated ${updatedCount} records in batch ${batchNumber}/${totalBatches}`
        );

        done(null, { status: true });
    } catch (error) {
        console.error(
            'Error in update_prvdr_vertical_for_srvc_reqs processor:',
            error
        );
        done(error);
    }
};

exports.default = performJob;
