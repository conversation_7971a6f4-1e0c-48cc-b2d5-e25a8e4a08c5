const { getServiceModelFrQueue } = require('./helpers/services_helper');
const {
    getServiceWorkflowModel,
} = require('./helpers/services_workflow_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const mainDb = app.get('db');
    const services_model = getServiceModelFrQueue(
        app,
        job.data.services_model_data
    );
    let workflow = getServiceWorkflowModel(services_model);
    let { query, new_entry_id, dbResp, is_customer_access, cust_org_id } =
        job.data;
    await workflow.processSrvcReqUpdationWorkFlowFrmVA(query, job.id);
    done(null, {});
};

exports.default = performJob;
