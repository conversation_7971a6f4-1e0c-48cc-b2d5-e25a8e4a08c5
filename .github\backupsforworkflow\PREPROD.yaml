name: Pre-PROD

# on:
#   workflow_dispatch: # Putting here is also fine!!
#   release:
#     types: [created]

on:
    pull_request:
        types: [opened, synchronize, reopened]
        # paths:
        #     - 'backend/App/**'
        #     - 'backend/Auth/**'

        #types: [opened, edited, reopened]

# Cancel prev CI if new commit come
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

env:
    AWS_REGION: ap-south-1 # set this to your preferred AWS region, e.g. us-west-1

    ECR_REPOSITORY_APP: tms-prod-app-copy
    ECR_REPOSITORY_DBSYNCOFF: tms-prod-appdbsyncoff-copy
    ECR_REPOSITORY_AUTH: prod-tms-auth-copy # set this to your Amazon ECR repository name

    ECS_SERVICE_APP: app-copy
    ECS_SERVICE_DBSYNCOFF: syncoff
    ECS_SERVICE_AUTH: auth-copy # set this to your Amazon ECS service name

    ECS_CLUSTER: tms-prod-copy

    ECS_TASK_DEFINITION_APP: .aws/v2/task-definition_APP.json
    CONTAINER_NAME_APP: tms-prod-app-copy # set this to the name of the container in the

    ECS_TASK_DEFINITION_DBSYNCOFF: .aws/v2/task-definition_DBSYNCOFF.json
    CONTAINER_NAME_DBSYNCOFF: tms-prod-appdbsyncoff-copy # set this to the name of the container in the

    ECS_TASK_DEFINITION_AUTH: .aws/v2/task-definition_AUTH.json
    CONTAINER_NAME_AUTH: prod-tms-auth-copy # set this to the name of the container in the

permissions:
    contents: read

jobs:
    notify-slack-1:
        # needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    # PreparingFrontEnd:
    #   runs-on: ubuntu-latest
    #   defaults:
    #     run:
    #       working-directory: ./frontend/react-app1

    #   steps:
    #     - name: Checkout repository
    #       uses: actions/checkout@v2

    #     - name: Use Node.js 16.14.0
    #       uses: actions/setup-node@v2
    #       with:
    #         node-version: 16.14.0

    #     - name: Install dependencies
    #       run: yarn install

    #     - uses: keithweaver/aws-s3-github-action@v1.0.0
    #       with:
    #         command: cp
    #         source: s3://wifybuildspec/tmscopy/front/.env.production
    #         destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.production
    #         aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #         aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #         aws_region: ap-south-1

    #     - name: List files in the repository
    #       run: |
    #         ls -a
    #         cat .env.production
    #         rm -rf .env
    #         # cp .env.production .env.production

    #     - name: Build application
    #       run: |
    #         echo "Building Project"
    #         yarn build-prod
    #       env:
    #         CI: false

    #     - name: Configure AWS credentials
    #       uses: aws-actions/configure-aws-credentials@v1
    #       with:
    #         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #         aws-region: ap-south-1

    #     - name: Remove files and folders from S3
    #       run: |
    #         aws s3 rm s3://internaltms.wify.co.in/ --recursive
    #       env:
    #         AWS_DEFAULT_REGION: ap-south-1

    #     - name: Deploy to S3
    #       run: aws s3 sync build s3://internaltms.wify.co.in --acl public-read
    #       working-directory: ./frontend/react-app1

    #     - name: Create CloudFront invalidation
    #       run: aws cloudfront create-invalidation --distribution-id E1Z9KR8LFQZE23 --paths "/*"

    Deploying-Dockers-APP:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/App/

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ env.AWS_REGION }}

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/app/Dockerfile'
                  dest: './backend/App/Dockerfile'

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/app/.env.production'
                  dest: './backend/App/.env.production'

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/app/validation.js'
                  dest: './backend/App/validation.js'

            - name: List files in the repository
              run: |
                  ls -a

            - name: Build, tag, and push image to Amazon ECRR
              id: build-image
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  IMAGE_TAG: ${{ github.sha }}
              run: |
                  # Build a docker container and
                  # push it to ECR so that it can
                  # be deployed to ECS.
                  docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_APP:$IMAGE_TAG .
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY_APP:$IMAGE_TAG
                  echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY_APP:$IMAGE_TAG"

            - name: Fill in the new image ID in the Amazon ECS task definition
              id: task-def
              uses: aws-actions/amazon-ecs-render-task-definition@v1
              with:
                  task-definition: ${{ env.ECS_TASK_DEFINITION_APP }}
                  container-name: ${{ env.CONTAINER_NAME_APP }}
                  image: ${{ steps.build-image.outputs.image }}

            - name: Deploy Amazon ECS task definition
              uses: aws-actions/amazon-ecs-deploy-task-definition@v1
              with:
                  task-definition: ${{ steps.task-def.outputs.task-definition }}
                  service: ${{ env.ECS_SERVICE_APP }}
                  cluster: ${{ env.ECS_CLUSTER }}
                  wait-for-service-stability: true
            - name: Slack Notification
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: avengers-github-updates
                  SLACK_COLOR: ${{ job.status }} # or a specific color like 'good' or '#ff00ff'
                  SLACK_ICON: https://lexica-serve-encoded-images2.sharif.workers.dev/full_jpg/48134d73-f57c-4f02-b36c-7b3bbc8897d5
                  SLACK_MESSAGE: 'Git Hub action Completed Successfully'
                  SLACK_TITLE: Post Title
                  SLACK_USERNAME: MrGithub

    Deploying-Dockers-DBSYNCOFF:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/App/

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ env.AWS_REGION }}

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/DBSYNCOFF/Dockerfile'
                  dest: './backend/App/Dockerfile'

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/DBSYNCOFF/.env.production'
                  dest: './backend/App/.env.production'

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/DBSYNCOFF/validation.js'
                  dest: './backend/App/validation.js'

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.production

            - name: Build, tag, and push image to Amazon ECRR
              id: build-image
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  IMAGE_TAG: ${{ github.sha }}
              run: |
                  # Build a docker container and
                  # push it to ECR so that it can
                  # be deployed to ECS.
                  docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_DBSYNCOFF:$IMAGE_TAG .
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY_DBSYNCOFF:$IMAGE_TAG
                  echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY_DBSYNCOFF:$IMAGE_TAG"

            - name: Fill in the new image ID in the Amazon ECS task definition
              id: task-def
              uses: aws-actions/amazon-ecs-render-task-definition@v1
              with:
                  task-definition: ${{ env.ECS_TASK_DEFINITION_DBSYNCOFF }}
                  container-name: ${{ env.CONTAINER_NAME_DBSYNCOFF }}
                  image: ${{ steps.build-image.outputs.image }}

            - name: Deploy Amazon ECS task definition
              uses: aws-actions/amazon-ecs-deploy-task-definition@v1
              with:
                  task-definition: ${{ steps.task-def.outputs.task-definition }}
                  service: ${{ env.ECS_SERVICE_DBSYNCOFF }}
                  cluster: ${{ env.ECS_CLUSTER }}
                  wait-for-service-stability: true
            - name: Slack Notification
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: avengers-github-updates
                  SLACK_COLOR: ${{ job.status }} # or a specific color like 'good' or '#ff00ff'
                  SLACK_ICON: https://lexica-serve-encoded-images2.sharif.workers.dev/full_jpg/48134d73-f57c-4f02-b36c-7b3bbc8897d5
                  SLACK_MESSAGE: 'Git Hub action Completed Successfully'
                  SLACK_TITLE: Post Title
                  SLACK_USERNAME: MrGithub

    Deploying-Dockers-AUTH:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/Auth/

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ env.AWS_REGION }}

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/auth/Dockerfile'
                  dest: './backend/Auth/Dockerfile'

            - name: Copy a file from s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: 's3://wifybuildspec/tmscopy/auth/.env.production'
                  dest: './backend/Auth/.env.production'

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.production

            - name: Build, tag, and push image to Amazon ECRR
              id: build-image
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  IMAGE_TAG: ${{ github.sha }}
              run: |
                  # Build a docker container and
                  # push it to ECR so that it can
                  # be deployed to ECS.
                  docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_AUTH:$IMAGE_TAG .
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY_AUTH:$IMAGE_TAG
                  echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY_AUTH:$IMAGE_TAG"

            - name: Fill in the new image ID in the Amazon ECS task definition
              id: task-def
              uses: aws-actions/amazon-ecs-render-task-definition@v1
              with:
                  task-definition: ${{ env.ECS_TASK_DEFINITION_AUTH }}
                  container-name: ${{ env.CONTAINER_NAME_AUTH }}
                  image: ${{ steps.build-image.outputs.image }}

            - name: Deploy Amazon ECS task definition
              uses: aws-actions/amazon-ecs-deploy-task-definition@v1
              with:
                  task-definition: ${{ steps.task-def.outputs.task-definition }}
                  service: ${{ env.ECS_SERVICE_AUTH }}
                  cluster: ${{ env.ECS_CLUSTER }}
                  wait-for-service-stability: true
            - name: Slack Notification
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
                  SLACK_CHANNEL: avengers-github-updates
                  SLACK_COLOR: ${{ job.status }} # or a specific color like 'good' or '#ff00ff'
                  SLACK_ICON: https://lexica-serve-encoded-images2.sharif.workers.dev/full_jpg/48134d73-f57c-4f02-b36c-7b3bbc8897d5
                  SLACK_MESSAGE: 'Git Hub action Completed Successfully'
                  SLACK_TITLE: Post Title
                  SLACK_USERNAME: MrGithub

    Deploying-ConsumerX:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./backend/App/

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ env.AWS_REGION }}

            - name: Upload files to S3
              run: |
                  aws s3 cp /home/<USER>/work/wify_tms_v2/wify_tms_v2/backend/App s3://wifybuildspec/tmscopy/consumer/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: AWS CodePipeline Trigger
              uses: zulhfreelancer/aws-codepipeline-action@v1.0.7
              with:
                  aws-region: 'ap-south-1'
                  aws-access-key: ${{ secrets.AWS_PIPELINE_ACCESS_KEY }}
                  aws-secret-key: ${{ secrets.AWS_PIPELINE_SECRET_KEY }}
                  pipeline-name: 'Copy-App'

    Status-Checks-For-URLs:
        needs:
            [
                Deploying-Dockers-APP,
                Deploying-Dockers-DBSYNCOFF,
                Deploying-Dockers-AUTH,
                Deploying-ConsumerX,
            ]
        runs-on: ubuntu-latest

        steps:
            - name: cURL the URL
              id: curl-request
              run: |
                  response=$(curl -s -o /dev/null -w "%{http_code}" https://api-tms-copy.wify.co.in/)
                  if [ "$response" != "200" ]; then
                    echo "cURL request failed with HTTP status code $response"
                    exit 1
                  fi
              continue-on-error: false

            - name: Check Status
              run: echo "cURL request was successful with HTTP status code 200"
              if: steps.curl-request.outcome == 'success'
