var sampleOperationResp = require('../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../utils/db_resp');
const pagination_filters_utils = require('../utils/pagination_filters_utils');
const users_model = require('../users_model');

class sbtsk_types_model {
    getSingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            var org_id = users_model.getOrgId(this.userContext);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_sbtsk_type_details(org_id, entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_sbtsk_type_details);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        this.getViewDataFrForm({}).then((operationResp) => {
                            // console.log(dbResp.data);
                            if (operationResp.isSuccess()) {
                                var finalResp = JSON.parse(operationResp.resp);
                                // console.log(dbResp.data);
                                // decode statuses
                                var finalStatuses = {};
                                dbResp.data.sbtsk_statuses_db.forEach(
                                    (element) => {
                                        // console.log("here");
                                        var status_type = element.status_type;
                                        if (
                                            finalStatuses[status_type] ==
                                            undefined
                                        ) {
                                            finalStatuses[status_type] = [];
                                        }
                                        delete element[status_type];
                                        finalStatuses[status_type].push(
                                            element
                                        );
                                    }
                                );
                                dbResp.data.sbtsk_statuses =
                                    JSON.stringify(finalStatuses); // text area
                                // end decoding statuses
                                finalResp.form_data = {
                                    ...dbResp.data.form_data,
                                    ...dbResp.data,
                                };
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(finalResp),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                resolve(operationResp);
                            }
                        });
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdate(query, entry_id = 0) {
        // console.log("Query rxd : ",JSON.stringify(query));
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            // console.log("form_data",form_data);
            var validationResp = this.validateCreateNewForm(query);
            if (!validationResp.isSuccess()) {
                resolve(validationResp);
                return;
            }
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_sbtsk_type(form_data, entry_id).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_sbtsk_type);

                    if (dbResp.code == 'title_or_key_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Title or Key not unique',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewDataFrForm(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            var form_data = JSON.stringify(query);
            // console.log("form_data-",form_data);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_srvc_type_getview_data(form_data).then(
                (res) => {
                    var srvcTypeViewResp = new db_resp(
                        res[0].tms_srvc_type_getview_data
                    );
                    if (!srvcTypeViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(srvcTypeViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAll(form) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(form);
            var org_id = users_model.getOrgId(this.userContext);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_sbtsk_types(
                    org_id,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_sbtsk_types);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getAllPossibleServiceTypes(query) {
        return new Promise((resolve, reject) => {
            try {
                //added new parameter
                const org_id = users_model.getOrgId(this.userContext);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db
                    .tms_hlpr_get_all_possible_srvc_types_fr_org(org_id)
                    .then(
                        (res) => {
                            var srvcTypeViewResp = new db_resp(
                                res[0].tms_hlpr_get_all_possible_srvc_types_fr_org
                            );
                            if (!srvcTypeViewResp.status) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            } else {
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(srvcTypeViewResp.data),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            }
                        },
                        (error) => {
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    validateCreateNewForm(form_data) {
        if (
            form_data['srvc_type_name'] == '' ||
            form_data['srvc_type_key'] == '' ||
            form_data['srvc_type_icon_selector'] == ''
        ) {
            return new sampleOperationResp(
                false,
                'Mandatory parameters missing!',
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getFreshInstance(model) {
        const clonedInstance = new sbtsk_types_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new sbtsk_types_model();
        return instance;
    }
}

module.exports = new sbtsk_types_model();
