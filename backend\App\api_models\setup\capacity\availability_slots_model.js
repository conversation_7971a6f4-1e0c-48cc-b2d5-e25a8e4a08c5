var sampleOperationResp = require('../../../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../../api_models/utils/db_resp');
const pagination_filters_utils = require('../../utils/pagination_filters_utils');
const users_model = require('../../users_model');
const { generateSlotsBasedOnParams } = require('./helpers');

class availability_slots_model {
    createOrUpdate(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['generated_slots'] = generateSlotsBasedOnParams(query);
                var form_data = JSON.stringify(query);

                if (!this.validateCreateNewForm(form_data)) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Please fill mandatory * field',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                let respData = (
                    await this.db.tms_ace_create_or_update_availability_slots_config(
                        JSON.stringify(query)
                    )
                )[0].tms_ace_create_or_update_availability_slots_config;
                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                console.error('availability_slots_model::create', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getViewData(query) {
        // console.log("Query rxd : ",query);
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                // console.log('Hello');
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                let respData = (
                    await this.db.tms_ace_viewdata_availability_slots(
                        JSON.stringify(query)
                    )
                )[0].tms_ace_viewdata_availability_slots;
                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                console.error('availability_slots_model::create', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    validateCreateNewForm(form_data) {
        if (
            form_data['day_start_time'] == '' ||
            form_data['day_end_time'] == '' ||
            form_data['slot_length'] == ''
        ) {
            return new sampleOperationResp(
                false,
                'Mandatory parameters missing!',
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new availability_slots_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new availability_slots_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new availability_slots_model();
