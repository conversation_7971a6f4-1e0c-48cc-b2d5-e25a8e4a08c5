const sampleOperationResp = require('../utils/operationResp');
const db_resp = require('../utils/db_resp');
var HttpStatus = require('http-status-codes');
const users_model = require('../users_model');

class booking_data_model {
    constructor() {
        this.db = null;
        this.dbReplica = null;
        this.dbDump = null;
        this.ip_address = null;
        this.user_agent_ = null;
        this.userContext = null;
    }

    getCapacityData(query) {
        return new Promise((resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
                const org_id_ = query.srvc_prvdr_id || query.org_id;
                const form_data = JSON.stringify(query);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db
                    .tms_hlpr_get_ace_capacity_settings_config_data_for_org(
                        org_id_
                    )
                    .then(
                        (res) => {
                            if (!res || !res[0]) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Unknown error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            }

                            const dbResp = new db_resp(
                                res[0].tms_hlpr_get_ace_capacity_settings_config_data_for_org
                            );

                            if (!dbResp.status) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );

                                return;
                            } else {
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(dbResp.data),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            }
                        },
                        (error) => {
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    getAvailabilitySlots(query) {
        return new Promise((resolve, reject) => {
            try {
                query['org_id'] = query.org_id;
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
                query['skill_id'] = process.env.BOOKING_SKILL_ID || 1;
                const form_data = JSON.stringify(query);
                //write booking available logic here
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db.tms_ace_get_time_slot_by_vertical(form_data).then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        const dbResp = new db_resp(
                            res[0].tms_ace_get_time_slot_by_vertical
                        );

                        if (dbResp.status === false) {
                            if (dbResp.code == 'pincode_missing') {
                                return resolve(
                                    new sampleOperationResp(
                                        false,
                                        dbResp.code,
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else if (
                                dbResp.code ==
                                'no_slots_available_for_this_pincode'
                            ) {
                                return resolve(
                                    new sampleOperationResp(
                                        false,
                                        dbResp.code,
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            } else {
                                return resolve(
                                    new sampleOperationResp(
                                        false,
                                        dbResp.data,
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                            }
                        }
                        // console.log('srvcReqs',JSON.stringify(srvcReqs));
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    getDayWiseCapacityData(query) {
        return new Promise((resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                const form_data = JSON.stringify(query);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                //   console.log('form_data', form_data);
                this.db.tms_ace_get_daywise_capacity_data(form_data).then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_ace_get_daywise_capacity_data
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        console.error(
                            'booking_data_model::getCityCapacityData',
                            error
                        );
                        resolve(
                            new sampleOperationResp(
                                false,
                                error?.message || 'Internal server error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                );
            } catch (error) {
                console.error('booking_data_model::getCityCapacityData', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getSlotWiseCapacityData(query) {
        return new Promise((resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['vertical_id'] = query.vertical_id;
                const form_data = JSON.stringify(query);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db.tms_ace_get_slotwise_capacity_data(form_data).then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_ace_get_slotwise_capacity_data
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.message || 'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        console.error(
                            'booking_data_model::getCitySlotsData',
                            error
                        );
                        resolve(
                            new sampleOperationResp(
                                false,
                                error?.message || 'Internal server error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                );
            } catch (error) {
                console.error('booking_data_model::getCitySlotsData', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }
    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set databaseDump(db) {
        this.dbDump = db;
    }

    get databaseDump() {
        return this.dbDump;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new booking_data_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new booking_data_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new booking_data_model();
