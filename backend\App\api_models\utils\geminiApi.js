const axios = require('axios');

function cleanResponseString(response) {
    return response.replace(/```json|```/g, '').trim();
}

function parseJsonStrings(input) {
    try {
        return JSON.parse(input, (_, value) =>
            typeof value === 'string' ? parseJsonStrings(value) : value
        );
    } catch (error) {
        return input;
    }
}

/**
 * Executes the gemini API with the given text and image queries.
 *
 * @param {Object} params - The parameters for the API call.
 * @param {string} params.textQuery - The text query for the API call.
 * @param {Array} params.imageQuery - The image query for the API call.
 * @return {Promise} A Promise that resolves to the response from the gemini API.
 */

/* imageQuery : 
  [
    "inline_data": {
      "data": base64
      "mime_type": mini-type eg: 'img', 'png', 'jpeg'
    }
  ]
*/
exports.geminiApi = async ({ textQuery, endFiles }) => {
    try {
        const s3Urls = [];

        /** s3 object :
         {
           type: image,
           url: img url eg: "https://static.wify.co.in/image_url.png",
           label: key from db eg: "general",
         }
        */
        for (const key in endFiles) {
            endFiles[key].map((file) => {
                s3Urls.push({
                    type: 'image',
                    url: file,
                    label: key,
                });
            });
        }
        const body = {
            userQuery: textQuery,
            s3Urls,
            jsonFormat: true,
        };

        const headers = {
            'x-api-key': process.env.AI_BACKEND_API_KEY,
        };
        const { data: geminiResponseRaw } = await axios.post(
            `${process.env.AI_BACKEND_BASE_URL}/gemini/text-generate`,
            body,
            {
                headers,
            }
        );
        console.log('gemini :: response :: ', geminiResponseRaw?.data);
        let geminiResponseRating = geminiResponseRaw?.data;

        return geminiResponseRating;
    } catch (error) {
        console.log('gemini :: error :: ', error?.message);
        return {
            error: error.message,
        };
    }
};
