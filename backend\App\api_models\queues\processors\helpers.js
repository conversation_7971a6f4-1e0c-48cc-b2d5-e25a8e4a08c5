var massive = require('massive');
const os = require('os');
var connectionString =
    process.env.DB_URL +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_BG_' + os.hostname(),
    }).toString();

exports.getDbForBgJob = (onDBConnected, onFailed) => {
    massive(connectionString)
        .then((massiveInstance) => {
            console.log('Database connection is established in background 👍');
            onDBConnected(massiveInstance);
        })
        .catch((e) => {
            console.log('getDbForBgJob failed', e);
            onFailed(e);
        });
};

exports.setParamsToServicesModel = (
    services_model,
    dataObj,
    massiveInstance
) => {
    services_model.db = massiveInstance;
    services_model.ip_addr = dataObj.ip_addr;
    services_model.user_agent = dataObj.user_agent;
    services_model.user_context = dataObj.user_context;
    services_model.srvc_type_id = dataObj.srvc_type_id;
    services_model.srvc_req_id = dataObj.srvc_req_id;
};
exports.getParamsToServiceModel = (dataObj, massiveInstance) => {
    const services_model = require('./../../services_model').getInstance();
    services_model.db = massiveInstance;
    services_model.ip_addr = dataObj.ip_addr;
    services_model.user_agent = dataObj.user_agent;
    services_model.user_context = dataObj.user_context;
    services_model.srvc_type_id = dataObj.srvc_type_id;
    services_model.srvc_req_id = dataObj.srvc_req_id;
    return services_model.getFreshInstance(services_model);
};

exports.setParamsToSubtaskModel = (subtask_model, dataObj, massiveInstance) => {
    subtask_model.database = massiveInstance;
    subtask_model.ip_addr = dataObj.ip_addr;
    subtask_model.user_agent = dataObj.user_agent;
    subtask_model.user_context = dataObj.user_context;
    subtask_model.srvc_req_id = dataObj.srvc_req_id;
};
exports.getParamsToSubtaskModel = (dataObj, massiveInstance) => {
    const subtask_model = require('./../../subtasks_model');
    subtask_model.database = massiveInstance;
    subtask_model.ip_addr = dataObj.ip_addr;
    subtask_model.user_agent = dataObj.user_agent;
    subtask_model.user_context = dataObj.user_context;
    subtask_model.srvc_req_id = dataObj.srvc_req_id;
    return subtask_model.getFreshInstance(subtask_model);
};

exports.setParamsToUserModel = (user_model, dataObj, massiveInstance) => {
    user_model.database = massiveInstance;
    user_model.ip_addr = dataObj.ip_addr;
    user_model.user_agent = dataObj.user_agent;
    user_model.user_context = dataObj.user_context;
    user_model.srvc_req_id = dataObj.srvc_req_id;
};

exports.setParamsToOrganisationsModel = (
    organisations_model,
    dataObj,
    massiveInstance
) => {
    organisations_model.database = massiveInstance;
    organisations_model.ip_addr = dataObj.ip_addr;
    organisations_model.user_agent = dataObj.user_agent;
    organisations_model.user_context = dataObj.user_context;
    organisations_model.srvc_req_id = dataObj.srvc_req_id;
};

exports.getOrganisationsModelModelFrQueue = (app, subtasks_model_data) => {
    const organisations_model = require('../../organisations_model');
    this.setParamsToOrganisationsModel(
        organisations_model,
        subtasks_model_data,
        app.get('db')
    );
    return organisations_model.getFreshInstance(organisations_model);
};

exports.setParamsToFeatureFlagModel = (model, dataObj, massiveInstance) => {
    model.database = massiveInstance;
    model.ip_addr = dataObj.ip_addr || dataObj.ip_address;
    model.user_agent = dataObj.user_agent || dataObj.user_agent_;
    model.user_context = dataObj.user_context || dataObj.userContext;
};

exports.setParamsToDumpsExportsModel = (model, dataObj, massiveInstance) => {
    model.database = massiveInstance;
    model.ip_addr = dataObj.ip_addr || dataObj.ip_address;
    model.user_agent = dataObj.user_agent || dataObj.user_agent_;
    model.user_context = dataObj.user_context || dataObj.userContext;
};

exports.getParamsToVaAiChatbotModel = (dataObj, massiveInstance) => {
    const va_ai_chatbot = require('./../../va_ai_chatbot_model');
    va_ai_chatbot.database = massiveInstance;
    va_ai_chatbot.ip_addr = dataObj.ip_addr;
    va_ai_chatbot.user_agent = dataObj.user_agent;
    va_ai_chatbot.user_context = dataObj.user_context;
    return va_ai_chatbot.getFreshInstance(va_ai_chatbot);
};
