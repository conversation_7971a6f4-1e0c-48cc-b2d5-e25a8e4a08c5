name: Automated Code Review

on:
    workflow_dispatch:
        inputs:
            pr_number:
                description: 'Pull Request Number'
                required: true
                type: string

permissions:
    contents: read
    pull-requests: write
    checks: write
    issues: write
    actions: read

jobs:
    code-review:
        runs-on: ubuntu-latest
        name: Automated Code Review

        steps:
            - name: Debug token availability
              run: |
                  echo "✅ Using built-in GITHUB_TOKEN"
                  echo "Repository: ${{ github.repository }}"
                  echo "Actor: ${{ github.actor }}"

            - name: Test GitHub API access
              run: |
                  # Test if the token works with GitHub API
                  response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
                    "https://api.github.com/repos/${{ github.repository }}")

                  http_code="${response: -3}"
                  if [ "$http_code" = "200" ]; then
                      echo "✅ GitHub API access successful"
                  else
                      echo "❌ GitHub API access failed with code: $http_code"
                      echo "Response: ${response%???}"
                      exit 1
                  fi

            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
                  token: ${{ secrets.GITHUB_TOKEN }}
                  persist-credentials: true

            - name: Setup Git credentials
              run: |
                  # Configure git to use the token
                  git config --global credential.helper store
                  echo "https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com" > ~/.git-credentials
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "GitHub Action"

                  # Also set the token as an environment variable for git
                  git config --global credential.https://github.com.username x-access-token
                  git config --global url."https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/".insteadOf "https://github.com/"

            - name: Fetch PR information
              run: |
                  # Get PR information using GitHub API
                  PR_INFO=$(curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
                    "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.inputs.pr_number }}")

                  # Extract branch information
                  HEAD_REF=$(echo "$PR_INFO" | jq -r '.head.ref')
                  BASE_REF=$(echo "$PR_INFO" | jq -r '.base.ref')
                  HEAD_SHA=$(echo "$PR_INFO" | jq -r '.head.sha')

                  echo "HEAD_REF=$HEAD_REF" >> $GITHUB_ENV
                  echo "BASE_REF=$BASE_REF" >> $GITHUB_ENV
                  echo "HEAD_SHA=$HEAD_SHA" >> $GITHUB_ENV

                  echo "PR Head Branch: $HEAD_REF"
                  echo "PR Base Branch: $BASE_REF"
                  echo "PR Head SHA: $HEAD_SHA"

            - name: Checkout PR branch
              run: |
                  # Fetch the specific PR branch
                  echo "Fetching PR branch: ${{ env.HEAD_REF }}"
                  git fetch origin ${{ env.HEAD_REF }}:${{ env.HEAD_REF }} || {
                      echo "Direct fetch failed, trying alternative method..."
                      git fetch origin pull/${{ github.event.inputs.pr_number }}/head:pr-${{ github.event.inputs.pr_number }}
                      git checkout pr-${{ github.event.inputs.pr_number }}
                      exit 0
                  }
                  git checkout ${{ env.HEAD_REF }}
                  echo "Successfully checked out branch: ${{ env.HEAD_REF }}"

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '18'
                  cache: 'npm'

            - name: Install system dependencies
              run: |
                  sudo apt-get update
                  sudo apt-get install -y jq

            - name: Install dependencies
              run: |
                  npm install -g eslint prettier @typescript-eslint/parser @typescript-eslint/eslint-plugin
                  npm install --save-dev eslint-plugin-react eslint-plugin-react-hooks

            - name: Setup config files
              run: |
                  cp .github/workflows/configs/eslintrc.js .eslintrc.js
                  cp .github/workflows/configs/prettierrc.js .prettierrc.js

            - name: Get changed files
              id: changed-files
              run: |
                  # Get files changed in the PR using GitHub API
                  curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
                    "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.inputs.pr_number }}/files" \
                    | jq -r '.[].filename' > changed_files.txt

                  echo "Changed files:"
                  cat changed_files.txt

                  # Filter for supported file types
                  grep -E '\.(js|jsx|ts|tsx|py|java|go|php|rb|cs|cpp|c|h)$' changed_files.txt > filtered_files.txt || true
                  echo "Filtered files:"
                  cat filtered_files.txt || echo "No supported files found"

            - name: Run ESLint on JavaScript/TypeScript files
              run: |
                  echo "Running ESLint on changed files..."
                  if [ -f "filtered_files.txt" ]; then
                      while IFS= read -r file; do
                          if [[ "$file" =~ \.(js|jsx|ts|tsx)$ ]] && [ -f "$file" ]; then
                              echo "Linting: $file"
                              npx eslint "$file" --format json > "eslint-$(basename $file).json" || true
                          fi
                      done < filtered_files.txt
                  fi

            - name: Run Prettier check
              run: |
                  echo "Checking code formatting with Prettier..."
                  if [ -f "filtered_files.txt" ]; then
                      while IFS= read -r file; do
                          if [[ "$file" =~ \.(js|jsx|ts|tsx|json|css|scss|md)$ ]] && [ -f "$file" ]; then
                              echo "Checking format: $file"
                              npx prettier --check "$file" || echo "Format issue in: $file" >> prettier-issues.txt
                          fi
                      done < filtered_files.txt
                  fi

            - name: Code Style & Logic Analysis
              run: |
                  echo "Analyzing code style and logic..."
                  if [ -f "filtered_files.txt" ]; then
                      while IFS= read -r file; do
                          if [[ "$file" =~ \.(js|jsx|ts|tsx)$ ]] && [ -f "$file" ]; then
                              echo "Analyzing: $file"

                              # File size check
                              lines=$(wc -l < "$file")
                              if [ "$lines" -gt 200 ]; then
                                  echo "📏 Large file: $file ($lines lines) - Consider breaking into smaller modules" >> style-logic-issues.txt
                              fi

                              # Function length check
                              awk '/function|=>|^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*[[:space:]]*\(/ {start=NR} /^[[:space:]]*}[[:space:]]*$/ {if(start && NR-start > 30) print "🔧 Long function at line " start " in '$file' (" NR-start " lines)"}' "$file" >> style-logic-issues.txt

                              # Deeply nested code (more than 4 levels)
                              max_nesting=$(awk '{
                                  for(i=1; i<=length($0); i++) {
                                      char = substr($0, i, 1)
                                      if(char == "{") level++
                                      if(char == "}") level--
                                      if(level > max) max = level
                                  }
                              } END {print max+0}' "$file")
                              if [ "$max_nesting" -gt 4 ]; then
                                  echo "🔄 Deep nesting detected in $file (level $max_nesting) - Consider refactoring" >> style-logic-issues.txt
                              fi

                              # Check for magic numbers
                              magic_numbers=$(grep -n '[^a-zA-Z_][0-9]\{2,\}[^a-zA-Z_]' "$file" | grep -v '//\|/\*\|\*/' || true)
                              if [ ! -z "$magic_numbers" ]; then
                                  echo "🔢 Magic numbers found in $file:" >> style-logic-issues.txt
                                  echo "$magic_numbers" >> style-logic-issues.txt
                              fi

                              # Check for inconsistent naming
                              camelCase=$(grep -o '[a-z][a-zA-Z0-9]*' "$file" | wc -l)
                              snake_case=$(grep -o '[a-z][a-z0-9_]*_[a-z0-9_]*' "$file" | wc -l)
                              if [ "$camelCase" -gt 0 ] && [ "$snake_case" -gt 0 ]; then
                                  echo "🏷️ Mixed naming conventions in $file (camelCase: $camelCase, snake_case: $snake_case)" >> style-logic-issues.txt
                              fi

                              # Check for long parameter lists
                              long_params=$(grep -n 'function.*([^)]\{50,\})' "$file" || true)
                              if [ ! -z "$long_params" ]; then
                                  echo "📝 Long parameter list in $file:" >> style-logic-issues.txt
                                  echo "$long_params" >> style-logic-issues.txt
                              fi

                              # Check for complex conditionals
                              complex_conditions=$(grep -n 'if.*&&.*||' "$file" || true)
                              if [ ! -z "$complex_conditions" ]; then
                                  echo "🤔 Complex conditional logic in $file:" >> style-logic-issues.txt
                                  echo "$complex_conditions" >> style-logic-issues.txt
                              fi
                          fi
                      done < filtered_files.txt
                  fi

            - name: Check for TODO/FIXME comments
              if: steps.changed-files.outputs.any_changed == 'true'
              run: |
                  echo "Checking for TODO/FIXME comments..."
                  for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
                    todos=$(grep -n -i "todo\|fixme\|hack\|xxx" "$file" || true)
                    if [ ! -z "$todos" ]; then
                      echo "📝 TODO/FIXME found in $file:" >> todo-issues.txt
                      echo "$todos" >> todo-issues.txt
                      echo "" >> todo-issues.txt
                    fi
                  done

            - name: Check for console.log/print statements
              if: steps.changed-files.outputs.any_changed == 'true'
              run: |
                  echo "Checking for debug statements..."
                  for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
                    if [[ "$file" =~ \.(js|jsx|ts|tsx)$ ]]; then
                      debug_statements=$(grep -n "console\." "$file" || true)
                      if [ ! -z "$debug_statements" ]; then
                        echo "🐛 Debug statements found in $file:" >> debug-issues.txt
                        echo "$debug_statements" >> debug-issues.txt
                        echo "" >> debug-issues.txt
                      fi
                    elif [[ "$file" =~ \.py$ ]]; then
                      debug_statements=$(grep -n "print(" "$file" || true)
                      if [ ! -z "$debug_statements" ]; then
                        echo "🐛 Print statements found in $file:" >> debug-issues.txt
                        echo "$debug_statements" >> debug-issues.txt
                        echo "" >> debug-issues.txt
                      fi
                    fi
                  done

            - name: Generate code review summary
              run: |
                  echo "# 🤖 Automated Code Review Summary" > review-summary.md
                  echo "" >> review-summary.md
                  echo "**PR:** #${{ github.event.inputs.pr_number }}" >> review-summary.md
                  echo "**Files changed:** $(wc -l < filtered_files.txt 2>/dev/null || echo 0)" >> review-summary.md
                  echo "**Target Branch:** ${{ env.BASE_REF }}" >> review-summary.md
                  echo "**Source Branch:** ${{ env.HEAD_REF }}" >> review-summary.md
                  echo "" >> review-summary.md

                  # Add ESLint results
                  if ls eslint-*.json 1> /dev/null 2>&1; then
                    echo "## 📋 ESLint Results" >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    cat eslint-*.json >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    echo "" >> review-summary.md
                  fi

                  # Add Prettier issues
                  if [ -f "prettier-issues.txt" ]; then
                    echo "## 🎨 Code Formatting Issues" >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    cat prettier-issues.txt >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    echo "" >> review-summary.md
                  fi

                  # Add complexity issues
                  if [ -f "complexity-issues.txt" ]; then
                    echo "## 🔍 Code Complexity Issues" >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    cat complexity-issues.txt >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    echo "" >> review-summary.md
                  fi

                  # Add TODO/FIXME comments
                  if [ -f "todo-issues.txt" ]; then
                    echo "## 📝 TODO/FIXME Comments" >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    cat todo-issues.txt >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    echo "" >> review-summary.md
                  fi

                  # Add debug statements
                  if [ -f "debug-issues.txt" ]; then
                    echo "## 🐛 Debug Statements Found" >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    cat debug-issues.txt >> review-summary.md
                    echo "\`\`\`" >> review-summary.md
                    echo "" >> review-summary.md
                  fi

                  echo "## ✅ Recommendations" >> review-summary.md
                  echo "- Remove debug statements before merging" >> review-summary.md
                  echo "- Fix formatting issues with Prettier" >> review-summary.md
                  echo "- Address ESLint warnings and errors" >> review-summary.md
                  echo "- Consider breaking down large files (>300 lines)" >> review-summary.md
                  echo "- Resolve TODO/FIXME comments or create issues" >> review-summary.md
                  echo "" >> review-summary.md
                  echo "---" >> review-summary.md
                  echo "*This review was generated automatically by GitHub Actions*" >> review-summary.md

            - name: Comment PR with review
              uses: actions/github-script@v7
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  script: |
                      const fs = require('fs');

                      // Read the review summary
                      let reviewContent = '';
                      try {
                        reviewContent = fs.readFileSync('review-summary.md', 'utf8');
                      } catch (error) {
                        reviewContent = '# 🤖 Automated Code Review\n\nNo issues found! ✅';
                      }

                      // Find existing review comment
                      const comments = await github.rest.issues.listComments({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        issue_number: context.issue.number,
                      });

                      const botComment = comments.data.find(comment => 
                        comment.user.type === 'Bot' && 
                        comment.body.includes('🤖 Automated Code Review')
                      );

                      if (botComment) {
                        // Update existing comment
                        await github.rest.issues.updateComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          comment_id: botComment.id,
                          body: reviewContent
                        });
                      } else {
                        // Create new comment
                        await github.rest.issues.createComment({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: context.issue.number,
                          body: reviewContent
                        });
                      }

            - name: Set PR status
              uses: actions/github-script@v7
              with:
                  github-token: ${{ secrets.GITHUB_TOKEN }}
                  script: |
                      const fs = require('fs');

                      // Check if there are any critical issues
                      let hasIssues = false;

                      if (fs.existsSync('eslint-*.json') || 
                          fs.existsSync('prettier-issues.txt') || 
                          fs.existsSync('debug-issues.txt')) {
                        hasIssues = true;
                      }

                      // Set commit status
                      await github.rest.repos.createCommitStatus({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        sha: context.payload.pull_request.head.sha,
                        state: hasIssues ? 'failure' : 'success',
                        target_url: `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}`,
                        description: hasIssues ? 'Code review found issues' : 'Code review passed',
                        context: 'Automated Code Review'
                      });
