name: Refresh Dev6
on:
    workflow_dispatch: # Putting here is also fine!!
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    notify-slack-1:
        # needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            # - uses: keithweaver/aws-s3-github-action@v1.0.0
            #   with:
            #       command: cp
            #       source: s3://wifytmsnodemodules/yarn.lock
            #       destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
            #       aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            #       aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            #       aws_region: ap-south-1

            # - name: Set Permissions for Yarn Lock File
            #   run: |
            #       sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install --network-timeout 1000000
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192 --openssl-legacy-provider'

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev6/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Free up memory before build
              run: |
                  echo "Freeing up system memory"
                  sudo apt-get clean
                  sudo rm -rf /tmp/*
                  df -h
                  free -h

            - name: Build application
              run: |
                  echo "Building Project with aggressive memory optimizations"
                  # Clear any existing build artifacts
                  rm -rf build/ || true
                  rm -rf node_modules/.cache || true
                  # Build with memory constraints
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: '--max_old_space_size=8192 --openssl-legacy-provider'
                  GENERATE_SOURCEMAP: false
                  INLINE_RUNTIME_CHUNK: false
                  BUILD_PATH: './build'
                  DISABLE_ESLINT_PLUGIN: true
                  TSC_COMPILE_ON_ERROR: true

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1
            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp6-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Generate unique merge ID
              id: generate-merge-id
              run: echo "version_id=$(uuidgen)" >> $GITHUB_ENV

            - name: List files in the repository
              run: |
                  ls -a build/

            - name: Update app_version.json
              run: |
                  echo "{ \"version_id\" : \"$version_id\" }" > build/app_version.json
              env:
                  version_id: ${{ env.version_id }}

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp6-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id E2T1N0YHKNKG9M --paths "/*"
    Deploying-Dockers:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev6/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev6/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev6/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev6/Auth'
                  flags: --recursive
    Start-Association:
        needs: [Deploying-Dockers]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "9326c265-798e-4d2b-94bd-dcaf303036df"

    notify-slack:
        needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
