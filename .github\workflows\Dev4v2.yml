name: Dev4v2
on:
    workflow_dispatch: # Putting here is also fine!!
        inputs:
            action:
                type: choice
                description: 'DEV4 or RefreshDEV4 or Only<PERSON><PERSON>tend or OnlyBackend'
                options:
                    - DEV4
                    - RefreshDEV4
                    - OnlyFrontend
                    - OnlyBackend
                required: true
                default: 'DEV4'
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    notify-slack-1:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev4/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp4-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp4-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id ERHCF052578BD --paths "/*"
    Preparing-DB-for-Dev4-ENV:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS 'tmsdev4' WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN Dev DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank Dev DB temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database 'tmsdev4' ;'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdev4'
                  backup_file: 'backups/backup.sql'

    Preparing-Redis-for-Dev4:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "bad92d64-92e0-46ac-b74b-46659f005051"

    Deploying-Dockers:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        needs: [Preparing-Redis-for-Dev4, Preparing-DB-for-Dev4-ENV]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/Auth'
                  flags: --recursive

    Start-Association:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        needs:
            [
                Deploying-Dockers,
                Preparing-Redis-for-Dev4,
                Preparing-DB-for-Dev4-ENV,
            ]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "07a3fab4-b34e-4ac6-b2f4-e7ac2774339e"
    notify-slack:
        if: ${{ github.event.inputs.action == 'DEV4' }}
        needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-2:
        if: ${{ github.event.inputs.action == 'RefreshDEV4' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd2:
        if: ${{ github.event.inputs.action == 'RefreshDEV4' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev4/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp4-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp4-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id ERHCF052578BD --paths "/*"

    Deploying-Dockers2:
        if: ${{ github.event.inputs.action == 'RefreshDEV4' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/Auth'
                  flags: --recursive

    Start-Association2:
        if: ${{ github.event.inputs.action == 'RefreshDEV4' }}
        needs: [Deploying-Dockers2]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "07a3fab4-b34e-4ac6-b2f4-e7ac2774339e"
    notify-slack2:
        if: ${{ github.event.inputs.action == 'RefreshDEV4' }}
        needs: [Deploying-Dockers2, PreparingFrontEnd2]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started! (OnlyFrontend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev4/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp4-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp4-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id ERHCF052578BD --paths "/*"

    notify-slack3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        needs: [PreparingFrontEnd3]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully! (OnlyFrontend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-4:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started! (OnlyBackend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    Deploying-Dockers3:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev4/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev4/Auth'
                  flags: --recursive

    Start-Association3:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        needs: [Deploying-Dockers3]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "07a3fab4-b34e-4ac6-b2f4-e7ac2774339e"
    notify-slack4:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        needs: [Deploying-Dockers3, Start-Association3]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully! (OnlyBackend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
