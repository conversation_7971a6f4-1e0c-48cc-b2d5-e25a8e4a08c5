name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
on:
    pull_request:
        types: [opened, synchronize, reopened]
        #types: [opened, edited, reopened]

#Cancel prev CI if new commit come
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    # Detect changes in different folders
    detect-changes:
        runs-on: ubuntu-latest
        outputs:
            auth-changed: ${{ steps.changes.outputs.auth }}
            app-changed: ${{ steps.changes.outputs.app }}
            frontend-changed: ${{ steps.changes.outputs.frontend }}
        steps:
            - name: Checkout repository
              uses: actions/checkout@v2
              with:
                  fetch-depth: 0

            - name: Detect changes
              id: changes
              run: |
                  # Get the list of changed files
                  if [ "${{ github.event_name }}" = "pull_request" ]; then
                      # For pull requests, compare against the base branch
                      CHANGED_FILES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD)
                  else
                      # For push events, compare against the previous commit
                      CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
                  fi

                  echo "Changed files:"
                  echo "$CHANGED_FILES"
                  echo ""
                  echo "Repository structure:"
                  echo "- frontend/ (android-app, react-app1)"
                  echo "- backend/ (App, Auth, Serverless, forwarder, AppForwarder)"
                  echo ""

                  # Check backend/Auth changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/Auth/"; then
                      echo "auth=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/Auth changes detected"
                  else
                      echo "auth=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/Auth changes"
                  fi

                  # Check backend/App changes
                  if echo "$CHANGED_FILES" | grep -q "^backend/App/"; then
                      echo "app=true" >> $GITHUB_OUTPUT
                      echo "✓ Backend/App changes detected"
                  else
                      echo "app=false" >> $GITHUB_OUTPUT
                      echo "✗ No Backend/App changes"
                  fi

                  # Check frontend changes (any subfolder)
                  if echo "$CHANGED_FILES" | grep -q "^frontend/"; then
                      echo "frontend=true" >> $GITHUB_OUTPUT
                      echo "✓ Frontend changes detected"

                      # Show which frontend components changed
                      if echo "$CHANGED_FILES" | grep -q "^frontend/android-app/"; then
                          echo "  - Android app changes detected"
                      fi
                      if echo "$CHANGED_FILES" | grep -q "^frontend/react-app1/"; then
                          echo "  - React app changes detected"
                      fi
                  else
                      echo "frontend=false" >> $GITHUB_OUTPUT
                      echo "✗ No Frontend changes"
                  fi

    # Backend Auth job - only runs when backend/Auth changes are detected
    For-Auth:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.auth-changed == 'true'
        defaults:
            run:
                working-directory: ./backend/Auth

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - run: yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Run tests and generate coverage report
              run: |
                  # yarn jest --detectOpenHandles
                  yarn test
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=4096

    # Backend App job - only runs when backend/App changes are detected
    For-App:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.app-changed == 'true'
        defaults:
            run:
                working-directory: ./backend/App

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.14.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.14.0

            - run: npm install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Run tests and generate coverage report
              run: |
                  # yarn jest --detectOpenHandles
                  yarn test
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=4096

    # Frontend job - only runs when frontend changes are detected
    For-Frontend:
        runs-on: ubuntu-latest
        needs: detect-changes
        if: needs.detect-changes.outputs.frontend-changed == 'true'
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 16.15.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.15.0

            - run: yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=4096

            - name: Use Node.js 16.15.0
              uses: actions/setup-node@v2
              with:
                  node-version: 16.15.0

            - name: Run tests and generate coverage report
              run: |
                  yarn test-units
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192

    Status-Checks-For-Jest:
        runs-on: ubuntu-latest
        needs: [detect-changes, For-Auth, For-App, For-Frontend]
        if: ${{ always() }}
        steps:
            - name: Check Previous Jobs
              id: check-request
              run: |
                  echo "Checking job results based on detected changes..."

                  # Check For-Auth if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.auth-changed }} == 'true' ]]; then
                    echo "Checking Auth job result..."
                    if [[ ${{ needs.For-Auth.result }} == 'failure' ]]; then
                      echo "❌ Auth Jest checks failed."
                      exit 1
                    else
                      echo "✅ Auth Jest checks passed."
                    fi
                  else
                    echo "⏭️ Auth job skipped (no changes detected)."
                  fi

                  # Check For-App if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.app-changed }} == 'true' ]]; then
                    echo "Checking App job result..."
                    if [[ ${{ needs.For-App.result }} == 'failure' ]]; then
                      echo "❌ App Jest checks failed."
                      exit 1
                    else
                      echo "✅ App Jest checks passed."
                    fi
                  else
                    echo "⏭️ App job skipped (no changes detected)."
                  fi

                  # Check For-Frontend if it was supposed to run
                  if [[ ${{ needs.detect-changes.outputs.frontend-changed }} == 'true' ]]; then
                    echo "Checking Frontend job result..."
                    if [[ ${{ needs.For-Frontend.result }} == 'failure' ]]; then
                      echo "❌ Frontend Jest checks failed."
                      exit 1
                    else
                      echo "✅ Frontend Jest checks passed."
                    fi
                  else
                    echo "⏭️ Frontend job skipped (no changes detected)."
                  fi

                  echo "🎉 All required Jest checks passed!"

            - name: Check Status
              run: echo "Failed"
              if: steps.check-request.outcome == 'Failure'
