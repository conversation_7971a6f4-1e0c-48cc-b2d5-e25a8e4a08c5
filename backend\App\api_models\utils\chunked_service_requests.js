const fs = require('fs').promises;
const path = require('path');

/**
 * Utility functions for managing chunked service request processing
 * Provides file-based caching and consolidation of service request data
 */

// Global temp directory path
const TEMP_DIR = path.join(
    __dirname,
    '../../temp_files/chunked_service_requests'
);

/**
 * Ensure the temporary directory exists
 */
async function ensureTempDirExists() {
    try {
        await fs.mkdir(TEMP_DIR, { recursive: true });
    } catch (error) {
        console.error('Error creating temp directory:', error);
    }
}

/**
 * Generate a unique filename for storing chunk data
 * @param {number} orgId - Organization ID
 * @param {number} verticalId - Vertical ID
 * @param {number} chunkIndex - Chunk index
 * @returns {string} Filename
 */
function getChunkFilename(orgId, verticalId, chunkIndex) {
    const timestamp = Date.now();
    return `srvc_reqs_${orgId}_${verticalId}_chunk_${chunkIndex}_${timestamp}.json`;
}

/**
 * Save chunk data to a temporary file
 * @param {Array} chunkData - Service request data for this chunk
 * @param {number} orgId - Organization ID
 * @param {number} verticalId - Vertical ID
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<string>} Filepath of saved chunk
 */
async function saveChunkData(chunkData, orgId, verticalId, chunkIndex) {
    await ensureTempDirExists();
    const filename = getChunkFilename(orgId, verticalId, chunkIndex);
    const filepath = path.join(TEMP_DIR, filename);

    try {
        await fs.writeFile(filepath, JSON.stringify(chunkData, null, 2));
        console.log(`Chunk ${chunkIndex} saved to: ${filepath}`);
        return filepath;
    } catch (error) {
        console.error(`Error saving chunk ${chunkIndex}:`, error);
        throw error;
    }
}

/**
 * Load and consolidate all chunk files for a specific org/vertical
 * @param {Array<string>} chunkFilepaths - Array of chunk file paths
 * @returns {Promise<Array>} Consolidated service request data
 */
async function consolidateChunks(chunkFilepaths) {
    let consolidatedData = [];

    for (const filepath of chunkFilepaths) {
        try {
            const chunkDataStr = await fs.readFile(filepath, 'utf8');
            const chunkData = JSON.parse(chunkDataStr);
            consolidatedData = consolidatedData.concat(chunkData);

            console.log(
                `Loaded chunk from: ${filepath}, records: ${chunkData.length}`
            );
        } catch (error) {
            console.error(`Error loading chunk from ${filepath}:`, error);
            // Continue with other chunks even if one fails
        }
    }

    return consolidatedData;
}

/**
 * Clean up temporary chunk files
 * @param {Array<string>} chunkFilepaths - Array of chunk file paths to delete
 */
async function cleanupChunkFiles(chunkFilepaths) {
    for (const filepath of chunkFilepaths) {
        try {
            await fs.unlink(filepath);
            console.log(`Cleaned up chunk file: ${filepath}`);
        } catch (error) {
            console.error(`Error cleaning up chunk file ${filepath}:`, error);
        }
    }
}

/**
 * Get chunking configuration based on data size estimates
 * @param {number} estimatedTotalRecords - Estimated total number of records
 * @returns {Object} Chunking configuration
 */
function getChunkingConfig(estimatedTotalRecords = 10000) {
    // Adjust chunk size based on estimated data volume
    // Note: totalMonths is now calculated dynamically in fetchServiceRequestsInChunks
    if (estimatedTotalRecords > 50000) {
        return {
            monthsPerChunk: 1,
            useFileCache: true,
        };
    } else if (estimatedTotalRecords > 20000) {
        return {
            monthsPerChunk: 2,
            useFileCache: true,
        };
    } else {
        return {
            monthsPerChunk: 3,
            useFileCache: false,
        };
    }
}

/**
 * Remove duplicate service requests based on srvc_req_id
 * @param {Array} serviceRequests - Array of service request objects
 * @returns {Array} Deduplicated array
 */
function deduplicateServiceRequests(serviceRequests) {
    const seen = new Set();
    const deduplicated = [];

    for (const req of serviceRequests) {
        if (!seen.has(req.srvc_req_id)) {
            seen.add(req.srvc_req_id);
            deduplicated.push(req);
        }
    }

    console.log(
        `Deduplication: ${serviceRequests.length} -> ${deduplicated.length} records`
    );
    return deduplicated;
}

module.exports = {
    ensureTempDirExists,
    getChunkFilename,
    saveChunkData,
    consolidateChunks,
    cleanupChunkFiles,
    getChunkingConfig,
    deduplicateServiceRequests,
};
