const { startTime<PERSON>he<PERSON> } = require('./helpers/start_time_checker_helper');

// Helper function to get the capacity export model
const getCapacityExportModelFrQueue = (app, requester) => {
    const capacity_dashboard_model = require('../../ace/capacity_dashboard_model');
    capacity_dashboard_model.database = app.get('db');
    capacity_dashboard_model.databaseReplica = app.get('db_replica');
    capacity_dashboard_model.ip_addr = requester?.ip_addr;
    capacity_dashboard_model.user_agent = requester?.user_agent;
    capacity_dashboard_model.user_context = requester?.userContext;
    return capacity_dashboard_model;
};

const performJob = async (job, done) => {
    const app = require('../../../app');
    const capacity_dashboard_model = getCapacityExportModelFrQueue(
        app,
        job.data.requester
    );
    let spentTimeObj = { spentTime: 0 };
    startTimeChecker(spentTimeObj, done);
    const resp = await capacity_dashboard_model.processPincodesNotInHubsExport(
        job.data
    );
    console.log('processPincodesNotInHubsExport Resp ', resp);
    spentTimeObj.spentTime = -1;
    done(null, {});
};

exports.default = performJob;
