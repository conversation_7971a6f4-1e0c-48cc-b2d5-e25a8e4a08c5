class srvc_status_update_via_VA_template {
    getSubjectFrSrvcStatusUpdate = (query, dbResp) => {
        const currentStatus = query?.provided_current_status;
        const newStatus = query?.provided_new_status;
        if (dbResp?.status == 'ALL_UPDATED') {
            return `TMS VA - Successfully updated the status of ${dbResp.success_entries?.length} requests from ${currentStatus} to ${newStatus} via Agent`;
        } else if (dbResp?.status == 'PARTIALLY_UPDATED') {
            return `TMS VA - Partially updated the status of ${dbResp.success_entries?.length} requests from ${currentStatus} to ${newStatus} via Agent`;
        } else {
            return `TMS VA - Failed to update status from ${currentStatus} to ${newStatus}`;
        }
    };

    srvcStatusUpdateViaVANotificationEmailTemplate = (
        query,
        dbResp,
        userName
    ) => {
        console.log(
            'srvcStatusUpdateViaVANotificationEmailTemplate :: dbResp',
            JSON.stringify(dbResp)
        );
        const errorKeyVsLabel = {
            no_update_access: 'You do not have request update access',
            srvc_status_missing_fields:
                'Mandatory service status movement field missing',
            is_restricted: 'Restricted access for SP',
            missing_required_fields: 'Missing required fields',
        };
        const currentStatus = query?.provided_current_status;
        const newStatus = query?.provided_new_status;
        let messageHeader = '';
        let messageBody = '';
        if (dbResp?.status == 'ALL_UPDATED') {
            const successEntries = dbResp?.success_entries;
            let successRows = '';

            for (let i = 0; i < successEntries.length; i += 2) {
                const leftSide = successEntries[i];
                const rightSide = successEntries[i + 1];

                successRows += `
                    <tr>
                        <td style="margin:0;font-size:14px;line-height:1rem;font-family:Arial,sans-serif;padding:0 0 10px 10px" width="50%" align="left">
                            • ${leftSide?.display_code}
                        </td>
                        ${
                            rightSide
                                ? `<td style="margin:0;font-size:14px;line-height:1rem;font-family:Arial,sans-serif;padding:0 0 10px 10px" width="50%" align="left">
                                    • ${rightSide?.display_code}
                                </td>`
                                : `<td width="50%"></td>`
                        }
                    </tr>`;
            }

            messageBody += `
                <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background:#f5f5f5ff;border-radius:10px;margin-bottom:10px;">
                    <tr>
                        <td colspan="2" style="font-size:18px;padding:10px;">
                            ${dbResp?.success_entries?.length} request(s) have been updated successfully from ${currentStatus} to ${newStatus}
                        </td>
                    </tr>
                    ${successRows}
                </table>`;

            messageHeader = 'success_header_fr_va_emailer';
        } else if (dbResp?.status == 'PARTIALLY_UPDATED') {
            messageHeader = 'partial_success_header_fr_va_emailer';
            const failedEntries = dbResp?.failed_entries || [];
            let result = {};
            failedEntries.forEach(({ error, display_code }) => {
                if (!result[errorKeyVsLabel[error]]) {
                    result[errorKeyVsLabel[error]] = [];
                }
                result[errorKeyVsLabel[error]].push({ display_code });
            });
            Object.entries(result).forEach(([errorType, entries]) => {
                let errorRows = '';
                for (let i = 0; i < entries.length; i += 2) {
                    const lefSide = entries[i];
                    const item2 = entries[i + 1];
                    errorRows += `
                        <tr>
                            <td style="padding:0 0 10px 10px" width="50%" align="left">• ${lefSide?.display_code}</td>
                            ${
                                item2
                                    ? `<td style="padding:0 0 10px 10px" width="50%" align="left">• ${item2?.display_code}</td>`
                                    : `<td style="padding:0 0 10px 10px" width="50%" align="left"></td>`
                            }
                        </tr>`;
                }

                messageBody += `
                    <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background: #f5f5f5;border-radius: 10px;margin-bottom: 10px;">
                        <tr>
                            <td colspan="2" style="font-size:18px;padding: 10px;">${errorType}</td>
                        </tr>
                        ${errorRows}
                    </table>`;
            });
        } else {
            const failedEntries = dbResp?.failed_entries || [];
            let result = {};
            failedEntries.forEach(({ error, display_code }) => {
                if (!result[errorKeyVsLabel[error]]) {
                    result[errorKeyVsLabel[error]] = [];
                }
                result[errorKeyVsLabel[error]].push({ display_code });
            });
            Object.entries(result).forEach(([errorType, entries]) => {
                let errorRows = '';
                for (let i = 0; i < entries.length; i += 2) {
                    const lefSide = entries[i];
                    const item2 = entries[i + 1];
                    errorRows += `
                        <tr>
                            <td style="padding:0 0 10px 10px" width="50%" align="left">• ${lefSide?.display_code}</td>
                            ${
                                item2
                                    ? `<td style="padding:0 0 10px 10px" width="50%" align="left">• ${item2?.display_code}</td>`
                                    : `<td style="padding:0 0 10px 10px" width="50%" align="left"></td>`
                            }
                        </tr>`;
                }

                messageBody += `
                    <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background: #f5f5f5;border-radius: 10px;margin-bottom: 10px;">
                        <tr>
                            <td colspan="2" style="font-size:18px;padding: 10px;">${errorType}</td>
                        </tr>
                        ${errorRows}
                    </table>`;
            });

            messageHeader = 'error_header_fr_va_emailer';
        }

        return `<html lang="en">
        <body>
            <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;background:#f5f5f5;">
                <tr>
                    <td align="center" style="padding:0;">
                        <table role="presentation" style="width:602px;border-collapse:collapse;border-spacing:0;text-align:left;">
                            <tr>
                                <td style="line-height:8px; height:8px;">&nbsp;</td>
                            </tr>                         
                            <tr>
                                <td align="center" style="padding:0; border-radius: 10px !important; overflow: hidden !important;">
                                    <img src="https://static.wify.co.in/images/tms/emailers/common_email_header_fr_va_orange_bg.png" alt="" style="height:auto;display:block;" />
                                </td>
                            </tr>                         
                            <tr>
                                <td>
                                    <tr>
                                        <td style="line-height:8px; height:8px;">&nbsp;</td>
                                    </tr>
                                </td>
                                <td>
                                    <tr>
                                        <td style="line-height:16px; height:16px; background-color: #FFFFFF;border-radius:10px 10px 0 0" >&nbsp;</td>
                                    </tr>
                                </td>
                                <td style="padding: 0 30px; background: #ffffff">
                                    <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;">
                                        <tr>
                                            <td style="padding:0 0 10px 0;color:#153643;">
                                                <h1 style="font-size:20px;margin:0;font-family:Arial,sans-serif;">Dear ${userName},</h1>         
                                            </td>
                                        </tr>
                                    </table>
                                    ${messageBody}                                    
                                </td>
                                <td>
                                    <tr>
                                        <td style="line-height:16px; height:16px; background-color: #FFFFFF;border-radius:0 0 10px 10px " >&nbsp;</td>
                                    </tr>
                                </td>
                                <td>
                                    <tr>
                                        <td style="line-height:8px; height:8px;">&nbsp;</td>
                                    </tr>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding:30px;background:#ffdcba; border-radius: 10px !important; overflow: hidden !important;">
                                    <table role="presentation" style="width:100%;border-collapse:collapse;border:0;border-spacing:0;font-size:9px;font-family:Arial,sans-serif;">
                                        <tr>
                                            <td style="padding:0;width:50%;" align="left">
                                                <p style="margin:0;font-size:14px;line-height:16px;font-family:Arial,sans-serif;color:#293c5c;">
                                                    &reg; TMS Vertical Agent powered by Wiffy<br/>
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
          </body>
      </html>`;
    };
}

module.exports = new srvc_status_update_via_VA_template();
