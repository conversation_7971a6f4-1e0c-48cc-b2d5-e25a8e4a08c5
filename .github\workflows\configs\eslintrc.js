module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true,
        jest: true,
    },
    extends: [
        'eslint:recommended',
        '@typescript-eslint/recommended',
        'plugin:react/recommended',
        'plugin:react-hooks/recommended',
    ],
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaFeatures: {
            jsx: true,
        },
        ecmaVersion: 12,
        sourceType: 'module',
    },
    plugins: ['react', 'react-hooks', '@typescript-eslint'],
    rules: {
        // Code Style Rules (High Priority)
        indent: ['error', 2],
        quotes: ['error', 'single'],
        semi: ['error', 'always'],
        'comma-dangle': ['error', 'always-multiline'],
        'object-curly-spacing': ['error', 'always'],
        'array-bracket-spacing': ['error', 'never'],
        'brace-style': ['error', '1tbs'],
        'comma-spacing': ['error', { before: false, after: true }],
        'key-spacing': ['error', { beforeColon: false, afterColon: true }],
        'space-before-blocks': 'error',
        'space-before-function-paren': ['error', 'never'],
        'space-in-parens': ['error', 'never'],
        'space-infix-ops': 'error',
        'keyword-spacing': 'error',
        'no-trailing-spaces': 'error',
        'no-multiple-empty-lines': ['error', { max: 2 }],
        'eol-last': 'error',

        // Code Logic Rules (High Priority)
        'no-unused-vars': 'error',
        'no-undef': 'error',
        'no-redeclare': 'error',
        'no-unreachable': 'error',
        'no-duplicate-case': 'error',
        'no-fallthrough': 'error',
        'default-case': 'warn',
        'consistent-return': 'error',
        'no-else-return': 'warn',
        'no-lonely-if': 'warn',
        'no-nested-ternary': 'error',
        'no-unneeded-ternary': 'error',
        'prefer-const': 'error',
        'no-var': 'error',
        eqeqeq: 'error',
        'no-implicit-coercion': 'warn',

        // Function and Variable Naming
        camelcase: ['error', { properties: 'always' }],
        'func-names': ['warn', 'as-needed'],
        'func-style': ['warn', 'declaration', { allowArrowFunctions: true }],

        // Complexity and Logic Flow
        complexity: ['warn', 8],
        'max-depth': ['warn', 3],
        'max-lines': ['warn', 200],
        'max-lines-per-function': ['warn', 30],
        'max-params': ['warn', 4],
        'max-statements': ['warn', 15],
        'max-statements-per-line': ['error', { max: 1 }],
        'no-magic-numbers': [
            'warn',
            {
                ignore: [0, 1, -1, 2],
                ignoreArrayIndexes: true,
                detectObjects: false,
            },
        ],

        // Code Quality and Logic
        'no-console': 'warn',
        'no-debugger': 'error',
        'no-alert': 'error',
        'no-duplicate-imports': 'error',
        'no-useless-call': 'error',
        'no-useless-return': 'error',
        'no-useless-concat': 'error',
        'no-useless-escape': 'error',
        'no-return-assign': 'error',
        'no-self-compare': 'error',
        'no-throw-literal': 'error',
        'no-unmodified-loop-condition': 'error',
        'no-loop-func': 'error',
        'no-new-wrappers': 'error',
        'no-new-object': 'error',
        'no-array-constructor': 'error',

        // React specific rules
        'react/prop-types': 'off',
        'react/react-in-jsx-scope': 'off',
        'react-hooks/rules-of-hooks': 'error',
        'react-hooks/exhaustive-deps': 'warn',
        'react/jsx-uses-react': 'error',
        'react/jsx-uses-vars': 'error',
        'react/jsx-key': 'error',
        'react/jsx-no-duplicate-props': 'error',
        'react/jsx-no-undef': 'error',
        'react/jsx-pascal-case': 'error',

        // TypeScript specific rules
        '@typescript-eslint/no-unused-vars': 'error',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        '@typescript-eslint/no-explicit-any': 'warn',
        '@typescript-eslint/prefer-const': 'error',
        '@typescript-eslint/no-var-requires': 'error',
    },
    settings: {
        react: {
            version: 'detect',
        },
    },
    ignorePatterns: [
        'node_modules/',
        'build/',
        'dist/',
        '*.min.js',
        'coverage/',
    ],
};
