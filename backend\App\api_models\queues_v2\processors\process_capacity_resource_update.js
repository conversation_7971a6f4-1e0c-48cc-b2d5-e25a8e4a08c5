const app = require('../../../app');

/**
 * Helper function to get the capacity update model with proper initialization
 * @returns {Object} - Initialized capacity update model
 */
const getCapacityUpdateModel = () => {
    const model = require('../../capacity_update/capacity_update_model');
    model.db = app.get('db');
    model.databaseReplica = app.get('db_replica');
    return model.getFreshInstance(model);
};

/**
 * Process capacity data for a specific resource combination
 * @param {Object} job - The job object from the queue
 * @param {Function} done - Callback to be called when the job is done
 */
const performJob = async (job, done) => {
    console.log(`${job.data.resourceId} | JOB: resource_update`);

    try {
        // Get the capacity update model
        const capacityUpdateModel = getCapacityUpdateModel();

        // Set IP and user agent from job data if available
        if (job.data.ip_address) {
            capacityUpdateModel.ip_addr = job.data.ip_address;
        }

        if (job.data.user_agent) {
            capacityUpdateModel.user_agent = job.data.user_agent;
        }

        // Extract resource data from job
        const {
            orgId,
            orgName,
            resourceId,
            providerId,
            verticalId,
            skillId,
            hubId,
            custom_date,
        } = job.data;

        // Call the model's processResourceCapacityUpdate method
        const result = await capacityUpdateModel.processResourceCapacityUpdate(
            orgId,
            orgName,
            resourceId,
            providerId,
            verticalId,
            skillId,
            hubId,
            custom_date
        );

        // Mark the job as completed with the result from the model
        done(null, result);
    } catch (error) {
        console.error('Error processing capacity resource update job:', error);
        // Mark the job as failed, but it will be retried based on the queue configuration
        done(error);
    }
};

exports.default = performJob;
