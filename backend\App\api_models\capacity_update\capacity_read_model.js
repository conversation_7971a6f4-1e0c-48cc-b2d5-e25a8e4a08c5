const sampleOperationResp = require('../utils/operationResp');
const HttpStatus = require('http-status-codes');

class CapacityReadModel {
    constructor() {
        this.db = null;
        this.ip_address = null;
        this.user_agent_ = null;
        this.userContext = null;
    }

    getCapacityFrResource(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'CapacityReadModel :: getCapacityFrResource :: STEP 1/4 - Request received'
                );

                // Safely deconstruct query parameters with defaults
                let resourceId = null,
                    startDate = null,
                    endDate = null,
                    vertical = null,
                    skill = null,
                    hub = null,
                    orgId = null;

                try {
                    // Extract values from query with fallbacks
                    resourceId = query.resourceId || null;
                    startDate = query.startDate || null;
                    endDate = query.endDate || null;
                    vertical = query.vertical || query.vertical_id || null;
                    skill = query.skill || query.skill_id || null;
                    hub = query.hub || query.hub_id || null;
                    orgId = query.orgId || query.org_id || null;

                    // If resourceId is not provided, check if we can construct it
                    if (!resourceId) {
                        // Check if we have the necessary components to construct the resourceId
                        if (orgId && vertical && hub) {
                            // Construct resourceId from orgId, vertical, skill (optional), and hub
                            // Format: orgId::text || '_' || vertical.db_id::text || '_' || skill.db_id::text || '_' || hub.id::text
                            resourceId = `${orgId}_${vertical}_${skill || '0'}_${hub}`;
                            console.log(
                                'CapacityReadModel :: getCapacityFrResource :: STEP 2/4 - ResourceId constructed'
                            );
                        } else {
                            console.error(
                                'CapacityReadModel :: getCapacityFrResource :: Cannot construct resourceId, missing required parameters'
                            );
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    {
                                        status: 'error',
                                        message: 'HUB_VERTICAL_MISSING',
                                    },
                                    HttpStatus.StatusCodes.BAD_REQUEST
                                )
                            );
                            return;
                        }
                    }
                } catch (parseError) {
                    console.error(
                        'CapacityReadModel :: getCapacityFrResource :: Error parsing query parameters:',
                        parseError
                    );
                }

                // Use database operations instead of lambda calls
                if (!this.db) {
                    console.error(
                        'CapacityReadModel :: getCapacityFrResource :: Database connection not available'
                    );
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify({
                                status: 'error',
                                message: 'Database connection not available',
                                timestamp: new Date().toISOString(),
                            }),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                // Set default dates if not provided
                if (!startDate || !endDate) {
                    // Default to tomorrow's date in UTC
                    const tomorrow = new Date();
                    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
                    tomorrow.setUTCHours(0, 0, 0, 0);

                    if (!startDate) {
                        startDate = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD format
                    }

                    if (!endDate) {
                        const nextDay = new Date(tomorrow);
                        nextDay.setUTCDate(nextDay.getUTCDate() + 1);
                        endDate = nextDay.toISOString().split('T')[0]; // YYYY-MM-DD format
                    }
                }

                console.log(
                    'CapacityReadModel :: getCapacityFrResource :: STEP 3/4 - Calling database function'
                );
                console.log(
                    'CapacityReadModel :: getCapacityFrResource :: resourceId:',
                    resourceId,
                    'startDate:',
                    startDate,
                    'endDate:',
                    endDate
                );

                // Use the database function to get capacity data
                const dbResponse =
                    await this.db.tms_ace_get_capacity_by_resource(
                        resourceId,
                        startDate,
                        endDate
                    );

                console.log(
                    'CapacityReadModel :: getCapacityFrResource :: STEP 4/4 - Processing database response'
                );

                // Parse the database function response
                const dbResult = dbResponse[0].tms_ace_get_capacity_by_resource;

                if (!dbResult.status) {
                    // Handle database function error
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(dbResult.data),
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }

                // Extract the response data from the database function
                const responseData = dbResult.data;

                console.log(
                    `CapacityReadModel :: getCapacityFrResource :: Found ${responseData.data.length} capacity records`
                );

                // Format the response to match the expected format in capacity_model.js
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(responseData),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'CapacityReadModel :: getCapacityFrResource',
                    error
                );
                // Format the error response to match the expected format in capacity_model.js
                resolve(
                    new sampleOperationResp(
                        false,
                        JSON.stringify({
                            status: 'error',
                            message:
                                error.message || 'An unexpected error occurred',
                            timestamp: new Date().toISOString(),
                        }),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    // Getter and setter methods
    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }

    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getFreshInstance(model) {
        const clonedInstance = new CapacityReadModel();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new CapacityReadModel();
