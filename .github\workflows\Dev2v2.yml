name: Dev2v2
on:
    workflow_dispatch: # Putting here is also fine!!
        inputs:
            action:
                type: choice
                description: 'DEV2 or RefreshDEV2 or Only<PERSON>rontend or OnlyBackend'
                options:
                    - DEV2
                    - RefreshDEV2
                    - OnlyFrontend
                    - OnlyBackend
                required: true
                default: 'DEV2'
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    notify-slack1-:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev2/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1
            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp2-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp2-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id EG2UCAF57ZZJ8 --paths "/*"
    Preparing-DB-for-Dev2-ENV:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS 'tmsdev2' WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN Dev DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank Dev DB temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database 'tmsdev2' ;'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdev2'
                  backup_file: 'backups/backup.sql'

    Preparing-Redis-for-Dev2:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "bcabf02d-d3fe-4307-8a09-1386b2ca71f9"

    Deploying-Dockers:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        needs: [Preparing-DB-for-Dev2-ENV, Preparing-Redis-for-Dev2]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/Auth'
                  flags: --recursive
    Start-Association:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        needs:
            [
                Deploying-Dockers,
                Preparing-Redis-for-Dev2,
                Preparing-DB-for-Dev2-ENV,
            ]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "098a235c-1113-4c5c-9c49-fca4ee6f205a"

    notify-slack:
        if: ${{ github.event.inputs.action == 'DEV2' }}
        needs: [Deploying-Dockers, PreparingFrontEnd]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-2:
        if: ${{ github.event.inputs.action == 'RefreshDEV2' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd2:
        if: ${{ github.event.inputs.action == 'RefreshDEV2' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev2/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1
            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp2-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp2-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id EG2UCAF57ZZJ8 --paths "/*"

    Deploying-Dockers2:
        if: ${{ github.event.inputs.action == 'RefreshDEV2' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/Auth'
                  flags: --recursive

    Start-Association2:
        if: ${{ github.event.inputs.action == 'RefreshDEV2' }}
        needs: [Deploying-Dockers2]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "098a235c-1113-4c5c-9c49-fca4ee6f205a"

    notify-slack2:
        if: ${{ github.event.inputs.action == 'RefreshDEV2' }}
        needs: [Deploying-Dockers2, PreparingFrontEnd2]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started! (OnlyFrontend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    PreparingFrontEnd3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend/react-app1

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Use Node.js 18
              uses: actions/setup-node@v2
              with:
                  node-version: 18
            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifytmsnodemodules/yarn.lock
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: Set Permissions for Yarn Lock File
              run: |
                  sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock

            - name: Install dependencies
              run: sudo yarn install
              env:
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - uses: keithweaver/aws-s3-github-action@v1.0.0
              with:
                  command: cp
                  source: s3://wifybuildspec/tmsdevenv/dev2/.env.dev
                  destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.dev
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws_region: ap-south-1

            - name: List files in the repository
              run: |
                  ls -a
                  cat .env.dev
                  rm -rf .env
                  cp .env.dev .env

            - name: Build application
              run: |
                  echo "Building Project"
                  yarn build-dev
              env:
                  CI: false
                  NODE_OPTIONS: --max_old_space_size=8192 --openssl-legacy-provider

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1
            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://devtemp2-tms/ --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Deploy to S3
              run: aws s3 sync build s3://devtemp2-tms --acl public-read
              working-directory: ./frontend/react-app1

            - name: Create CloudFront invalidation
              run: aws cloudfront create-invalidation --distribution-id EG2UCAF57ZZJ8 --paths "/*"

    notify-slack3:
        if: ${{ github.event.inputs.action == 'OnlyFrontend' }}
        needs: [PreparingFrontEnd3]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully! (OnlyFrontend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL

    notify-slack-4:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started! (OnlyBackend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
    Deploying-Dockers3:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/App --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Remove files and folders from S3
              run: |
                  aws s3 rm s3://wifybuildspec/tmsdevdockers/tmsdev2/Auth --recursive
              env:
                  AWS_DEFAULT_REGION: ap-south-1

            - name: Copy App to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/App'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/App'
                  flags: --recursive

            - name: Copy Auth to s3
              uses: prewk/s3-cp-action@v2
              with:
                  aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  source: './backend/Auth'
                  dest: 's3://wifybuildspec/tmsdevdockers/tmsdev2/Auth'
                  flags: --recursive

    Start-Association3:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        needs: [Deploying-Dockers3]
        runs-on: ubuntu-latest
        steps:
            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-south-1

            - name: Start Association
              run: |
                  aws ssm start-associations-once --association-ids "098a235c-1113-4c5c-9c49-fca4ee6f205a"

    notify-slack4:
        if: ${{ github.event.inputs.action == 'OnlyBackend' }}
        needs: [Deploying-Dockers3, Start-Association3]
        runs-on: ubuntu-latest
        steps:
            - name: Retrieve branch name and workflow name
              run: |
                  echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
                  echo "Workflow: ${{ github.event.workflow }}"
                  echo "Actor name: $GITHUB_ACTOR"
                  echo "Workflow name: $GITHUB_WORKFLOW"
            - name: Send Slack notification
              env:
                  SLACK_WEBHOOK_URL: *********************************************************************************
              run: |
                  curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully! (OnlyBackend)"}' $SLACK_WEBHOOK_URL

                  curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
