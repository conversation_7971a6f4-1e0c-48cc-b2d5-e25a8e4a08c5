class pagination_filters_utils {
    decodeQueryParams(form, removeNull = false) {
        let pagination = {};
        let filters_ = {};
        try {
            pagination = JSON.parse(form.pagination || '{}');
            filters_ = JSON.parse(form.filters || '{}');
        } catch (e) {
            console.error(
                'pagination_filters_utils :: decodeQueryParams :: Error parsing pagination or filters:',
                e.message
            );
        }

        var page_no_ = pagination.current;
        var page_size_ = pagination.pageSize;
        var search_query = form.search_query;

        var filtersAllKeys = Object.keys(filters_);
        for (var i = 0; i < filtersAllKeys.length; i++) {
            let singleKey = filtersAllKeys[i];
            let filterValue = filters_[singleKey];
            if (removeNull && filterValue === null) {
                delete filters_[singleKey];
                continue;
            }
            if (typeof filterValue == 'string' && filterValue == '-1') {
                // console.log("filterValue",filterValue);
                delete filters_[singleKey];
            } else if (
                filterValue?.length > 0 &&
                Array.isArray(filterValue) &&
                filterValue.includes('-1')
            ) {
                delete filters_[singleKey];
            }
            // else if(filterValue[0]=='-1'){
            //     filters_[singleKey].pop();
            // }
        }
        filters_ = JSON.stringify(filters_);
        return { page_no_, page_size_, search_query, filters_ };
    }
}

module.exports = new pagination_filters_utils();
