class srvc_req_send_for_billing_notification_temp {
    SrvcReqSendForBillingNotificationTemplate = (notifyData) => {
        return `<html lang="en">
      <body>
        <div>
            Hello,
            
            <br />
            <br />

            ${notifyData?.req_sender_name} has requested billing for site ID ${notifyData?.ticket_id}. Kindly review the following details and lock request for billing.
            <br />
            <br />
           
            <table style="width:30%; border:1px solid #ccc">
              <tr>
                <td style="border:1px solid #ccc"> Brand name </td>
                <td style="border:1px solid #ccc"> ${notifyData?.org_details?.nickname} </td>
              </tr>
              <tr>
                <td style="border:1px solid #ccc"> Description </td>
                <td style="border:1px solid #ccc"> ${notifyData?.description} </td>
              </tr>
              <tr>
                <td style="border:1px solid #ccc"> Discount </td>
                <td style="border:1px solid #ccc"> ${notifyData?.discount || 0} </td>
              </tr>
              <tr>
                <td style="border:1px solid #ccc"> Amount after discount </td>
                <td style="border:1px solid #ccc"> ${notifyData?.final_amount || 0} </td>
              </tr>
            </table>

            <br/>
                <p>Click <a href=${notifyData?.targetLink}>here</a> to view on TMS.</p>
          </div>
        </body>
    </html>`;
    };
}

module.exports = new srvc_req_send_for_billing_notification_temp();
