name: Deploy to Env QA5
on:
  workflow_dispatch: # Putting here is also fine!!
  release:
    types: [created]

env:
  PGPASSWORD: ${{ secrets.dev_db_cred_1 }}

jobs:
  notify-slack-1:
    # needs: [Deploying-Dockers, PreparingFrontEnd]
    runs-on: ubuntu-latest
    steps:
      - name: Retrieve branch name and workflow name
        run: |
          echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
          echo "Workflow: ${{ github.event.workflow }}"
          echo "Actor name: $GITHUB_ACTOR"
          echo "Workflow name: $GITHUB_WORKFLOW"
      - name: Send Slack notification
        env:
          SLACK_WEBHOOK_URL: *********************************************************************************
        run: |
          curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" started!"}' $SLACK_WEBHOOK_URL

          curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
  PreparingFrontEnd:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend/react-app1

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Use Node.js 16.14.0
        uses: actions/setup-node@v2
        with:
          node-version: 16.14.0


      - name: Print npm version
        run: |
         echo "npm version: $(npm -v)"

      - name: List files in the repository
        run: |
          ls -a
          echo node --version
          echo "npm version: $(npm -v)"
          echo "Node.js version: $(node -v)"
      
      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: s3://wifytmsnodemodules/yarn.lock
          destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ap-south-1

      - name: Set Permissions for Yarn Lock File
        run: |
          sudo chmod 644 /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/yarn.lock
      
      - name: Install dependencies
        run: sudo yarn install
        env:
          NODE_OPTIONS: --max_old_space_size=4096

      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: s3://wifybuildspec/tmsdevenv/qa05/.env.dev
          destination: /home/<USER>/work/wify_tms_v2/wify_tms_v2/frontend/react-app1/.env.test
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ap-south-1



      - name: List files in the repository
        run: |
          ls -a
          cat .env.test
          rm -rf .env
          cp .env.test .env


      - name: Build application
        run: |
          echo "Building Project"
          yarn build-uat
        env:
          CI: false
          NODE_OPTIONS: --max_old_space_size=4096

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Remove files and folders from S3
        run: |
          aws s3 rm s3://tmsqa5/ --recursive
        env:
          AWS_DEFAULT_REGION: ap-south-1

      - name: Deploy to S3
        run: aws s3 sync build s3://tmsqa5 --acl public-read
        working-directory: ./frontend/react-app1

      - name: Create CloudFront invalidation
        run: aws cloudfront create-invalidation --distribution-id E14W2UJZIY0FET --paths "/*"
  Preparing-DB-for-QA05-ENV:
    runs-on: ubuntu-latest

    steps:
      - name: Start PostgreSQL on Ubuntu and Drop the OLD DB
        run: |
          echo Installing psql
          sudo apt-get install -y postgresql-client
          echo connecting with rds db
          psql -h ${{ secrets.dev_db_endp }} -d wify_tms_uat -U postgres -c 'DROP DATABASE IF EXISTS 'tmsqa5' WITH ( FORCE ) ;'

      - name: Postgres Dump Backup for ORIGIN  DB
        uses: tj-actions/pg-dump@v2.3
        with:
          database_url: "postgres://postgres:${{ secrets.dev_db_cred_1 }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms_uat"
          path: "backups/backup.sql"
          options: "-O"

      - name: Start PostgreSQL on Ubuntu and Create blank  DB temp
        run: |
          echo Installing psql
          sudo apt-get install -y postgresql-client
          echo connecting with rds db
          psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U postgres -c 'create database 'tmsqa5' ;'

      - name: Postgres Backup Restore from Origin  to new DB  temp
        uses: tj-actions/pg-restore@v4.5
        with:
          database_url: "postgres://postgres:${{ secrets.dev_db_cred_1 }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsqa5"
          backup_file: "backups/backup.sql"

  Preparing-Redis-for-QA05:
    #needs: Preparing-DB-for-QA02-ENV
    runs-on: ubuntu-latest
    steps:
      - name: multiple command
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.lowerenv_server }}
          username: ubuntu
          timeout: 6000s
          command_timeout: 6000s
          password: ${{ secrets.lowerenv_server_cred }}
          port: 3344
          script: sh /home/<USER>/qa05/script.sh

  Deploying-Dockers:
    needs: [Preparing-Redis-for-QA05, Preparing-DB-for-QA05-ENV]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Remove files and folders from S3
        run: |
          aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/App --recursive
        env:
          AWS_DEFAULT_REGION: ap-south-1

      - name: Remove files and folders from S3
        run: |
          aws s3 rm s3://wifybuildspec/tmsdevdockers/qa05/Auth --recursive
        env:
          AWS_DEFAULT_REGION: ap-south-1

      - name: Copy App to s3
        uses: prewk/s3-cp-action@v2
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          source: "./backend/App"
          dest: "s3://wifybuildspec/tmsdevdockers/qa05/App"
          flags: --recursive

      - name: Copy Auth to s3
        uses: prewk/s3-cp-action@v2
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          source: "./backend/Auth"
          dest: "s3://wifybuildspec/tmsdevdockers/qa05/Auth"
          flags: --recursive

      - name: multiple command
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.lowerenv_server }}
          username: ubuntu
          password: ${{ secrets.lowerenv_server_cred }}
          port: 3344
          script: |
            cd /home/<USER>/qa05
            rm -rf /home/<USER>/qa05/App
            rm -rf /home/<USER>/qa05/Auth
            aws s3 cp s3://wifybuildspec/tmsdevdockers/qa05/App/ /home/<USER>/qa05/App/ --recursive
            aws s3 cp s3://wifybuildspec/tmsdevdockers/qa05/Auth/ /home/<USER>/qa05/Auth/ --recursive
            aws ssm get-parameter --region=ap-south-1  --name /tms/be/qa05/.env.test  --output text --query Parameter.Value > /home/<USER>/qa05/App/.env.test
            aws ssm get-parameter --region=ap-south-1  --name /tms/be/auth/qa05/.env.test  --output text --query Parameter.Value > /home/<USER>/qa05/Auth/.env.test
            aws ssm get-parameter --region=ap-south-1  --name /tms/backend/qa05/Dockerfile  --output text --query Parameter.Value > /home/<USER>/qa05/App/Dockerfile
            aws ssm get-parameter --region=ap-south-1  --name /tms/backend/qa05/auth/Dockerfile  --output text --query Parameter.Value > /home/<USER>/qa05/Auth/Dockerfile
            cp /home/<USER>/validation.js /home/<USER>/qa05/App
            echo "Stopping dockers running" 
            docker kill tms-qa5-app5
            docker kill tms-qa5-auth5
            docker kill redis15
            echo "removing old docker image"
            docker rmi -f tms-qa5-app5
            docker rmi -f tms-qa5-auth5
            docker rmi -f redis
            echo "Building fresh docker image"
            cd /home/<USER>/qa05/App
            docker build -t tms-qa5-app5 .
            cd /home/<USER>/qa05/Auth
            docker build -t tms-qa5-auth5 .
            echo "Running new docker image"
            cd /home/<USER>/qa05/
            docker build -t redis15 .
            docker rm `docker ps --no-trunc -aq`
            #dont forget to change in redis.conf in folder
            docker run --name redis15 --network qa05 -d -p 9666:6379 redis15
            sleep 2
            docker run -d -v /home/<USER>/logs:/logs  --restart unless-stopped  -p 9444:9444 --cpus=".5"  --network qa05 --name tms-qa5-app5 tms-qa5-app5 
            docker run -d --restart unless-stopped  -p 9555:9555  --network qa05 --name tms-qa5-auth5 tms-qa5-auth5
            echo "Success"
  notify-slack:
    needs: [Deploying-Dockers, PreparingFrontEnd]
    runs-on: ubuntu-latest
    steps:
      - name: Retrieve branch name and workflow name
        run: |
          echo "echo "Current branch: ${GITHUB_REF#refs/heads/}""
          echo "Workflow: ${{ github.event.workflow }}"
          echo "Actor name: $GITHUB_ACTOR"
          echo "Workflow name: $GITHUB_WORKFLOW"
      - name: Send Slack notification
        env:
          SLACK_WEBHOOK_URL: *********************************************************************************
        run: |
          curl -X POST -H 'Content-type: application/json' --data '{"text":"GitHub Action \"'"${GITHUB_WORKFLOW}"'\" on branch \"'"${GITHUB_REF#refs/heads/}"'\" completed successfully!"}' $SLACK_WEBHOOK_URL

          curl -X POST -H 'Content-type: application/json' --data '{"text":"Actor name: '"$GITHUB_ACTOR"'\nWorkflow name: '"$GITHUB_WORKFLOW"'\nCurrent branch: '"${GITHUB_REF#refs/heads/}"'"}' $SLACK_WEBHOOK_URL
