var sampleOperationResp = require('../../../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../../api_models/utils/db_resp');
const pagination_filters_utils = require('../../utils/pagination_filters_utils');
const users_model = require('../../users_model');

class capacity_settings_model {
    createOrUpdate(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                const form_data = JSON.stringify(query);

                if (!this.validateCreateNewForm(form_data)) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Please fill mandatory * field',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                let respData = (
                    await this.db.tms_create_or_update_org_level_capacity_settings(
                        JSON.stringify(query)
                    )
                )[0].tms_create_or_update_org_level_capacity_settings;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getViewData(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                let respData = (
                    await this.db.tms_get_org_level_capacity_settings_data(
                        JSON.stringify(query)
                    )
                )[0].tms_get_org_level_capacity_settings_data;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    validateCreateNewForm(form_data) {
        // Parse the form data if it's a string
        const formData =
            typeof form_data === 'string' ? JSON.parse(form_data) : form_data;

        // If capacity module is enabled, demand_translation_arn is required
        if (
            formData.enable_capacity_module === true &&
            (!formData.demand_translation_arn ||
                formData.demand_translation_arn === '')
        ) {
            return new sampleOperationResp(
                false,
                'Demand translator ARN is required when capacity module is enabled',
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }

        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getInstance() {
        const instance = new capacity_settings_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new capacity_settings_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new capacity_settings_model();
