var sampleOperationResp = require('../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../utils/db_resp');
const users_model = require('../users_model');

class InventoryCustomFields {
    getInventoryCustomFields(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_inventory_custom_field_view_data(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_inventory_custom_field_view_data
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateInventoryCustomFields(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['entry_id'] = entry_id;
            var form_data = JSON.stringify(query);

            if (!this.validateCustForm(form_data)) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Please fill mandatory * field',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_create_or_update_inventory_custom_fields(
                    form_data,
                    entry_id
                )
                .then(
                    (res) => {
                        var billingFieldResp = new db_resp(
                            res[0].tms_create_or_update_inventory_custom_fields
                        );

                        if (!billingFieldResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(billingFieldResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    validateCustForm(form_data) {
        return true;
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new InventoryCustomFields();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new InventoryCustomFields();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new InventoryCustomFields();
